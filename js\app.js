/**
 * Vietnamese Accounting App - Main Application Controller
 * <PERSON><PERSON> thủ Thông tư 133/2016/TT-BTC
 */

// Main application class
class VietnameseAccountingApp {
    constructor() {
        this.currentView = 'dashboard';
        this.transactions = [];
        this.companyInfo = {};
        this.settings = {};
        this.isInitialized = false;

        // Bind methods
        this.init = this.init.bind(this);
        this.handleNavigation = this.handleNavigation.bind(this);
        this.handleTabSwitch = this.handleTabSwitch.bind(this);
        this.handleCategoryClick = this.handleCategoryClick.bind(this);
        this.updateQuickStats = this.updateQuickStats.bind(this);
        this.loadRecentTransactions = this.loadRecentTransactions.bind(this);
    }

    /**
     * Initialize the application
     */
    async init() {
        try {
            // Show loading screen
            this.showLoadingScreen();

            // Initialize storage
            AppStorage.init();

            // Load data
            await this.loadData();

            // Setup event listeners
            this.setupEventListeners();

            // Initialize UI
            this.initializeUI();

            // Hide loading screen and show app
            this.hideLoadingScreen();

            this.isInitialized = true;
            console.log('Vietnamese Accounting App initialized successfully');

        } catch (error) {
            console.error('Failed to initialize app:', error);
            this.showError('Lỗi khởi tạo ứng dụng: ' + error.message);
        }
    }

    /**
     * Show loading screen
     */
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const app = document.getElementById('app');

        if (loadingScreen) loadingScreen.style.display = 'flex';
        if (app) app.style.display = 'none';
    }

    /**
     * Hide loading screen and show app
     */
    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const app = document.getElementById('app');

        setTimeout(() => {
            if (loadingScreen) loadingScreen.style.display = 'none';
            if (app) app.style.display = 'block';
        }, 1000); // Show loading for at least 1 second for better UX
    }

    /**
     * Load application data
     */
    async loadData() {
        try {
            // Load transactions
            this.transactions = AppStorage.getTransactions();

            // Load company info
            this.companyInfo = AppStorage.getCompanyInfo();

            // Load settings
            this.settings = AppStorage.getSettings();

            console.log('Data loaded:', {
                transactions: this.transactions.length,
                companyInfo: this.companyInfo,
                settings: this.settings
            });

        } catch (error) {
            console.error('Error loading data:', error);
            throw new Error('Không thể tải dữ liệu ứng dụng');
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', this.handleNavigation);
        });

        // Tab switcher
        document.querySelectorAll('.tab-switch-btn').forEach(btn => {
            btn.addEventListener('click', this.handleTabSwitch);
        });

        // Category buttons
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.addEventListener('click', this.handleCategoryClick);
        });

        // Settings button
        const settingsBtn = document.getElementById('settings-btn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.showSettingsModal();
            });
        }

        // View all transactions button
        const viewAllBtn = document.getElementById('view-all-transactions');
        if (viewAllBtn) {
            viewAllBtn.addEventListener('click', () => {
                this.switchView('transactions');
            });
        }

        // Settings modal close
        const settingsClose = document.getElementById('settings-close');
        if (settingsClose) {
            settingsClose.addEventListener('click', () => {
                this.hideSettingsModal();
            });
        }

        // Period selector
        const periodSelect = document.getElementById('current-period');
        if (periodSelect) {
            periodSelect.addEventListener('change', (e) => {
                this.handlePeriodChange(e.target.value);
            });
        }

        // Custom date range
        const startDate = document.getElementById('start-date');
        const endDate = document.getElementById('end-date');
        if (startDate && endDate) {
            startDate.addEventListener('change', () => this.handleCustomDateRange());
            endDate.addEventListener('change', () => this.handleCustomDateRange());
        }

        // Window resize handler
        window.addEventListener('resize', AppHelpers.debounce(() => {
            this.handleResize();
        }, 250));

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }

    /**
     * Initialize UI components
     */
    initializeUI() {
        // Update company info in header
        this.updateCompanyInfo();

        // Update quick stats
        this.updateQuickStats();

        // Load recent transactions
        this.loadRecentTransactions();

        // Set initial view
        this.switchView('dashboard');
    }

    /**
     * Handle navigation clicks
     */
    handleNavigation(e) {
        e.preventDefault();
        const link = e.currentTarget;
        const view = link.getAttribute('data-view');

        if (view) {
            this.switchView(view);
        }
    }

    /**
     * Switch between views
     */
    switchView(viewName) {
        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        const activeNavItem = document.querySelector(`[data-view="${viewName}"]`)?.closest('.nav-item');
        if (activeNavItem) {
            activeNavItem.classList.add('active');
        }

        // Update content views
        document.querySelectorAll('.content-view').forEach(view => {
            view.classList.remove('active');
        });

        const targetView = document.getElementById(`${viewName}-view`);
        if (targetView) {
            targetView.classList.add('active');
        }

        this.currentView = viewName;

        // Update URL hash
        window.location.hash = viewName;
    }

    /**
     * Handle tab switching
     */
    handleTabSwitch(e) {
        const btn = e.currentTarget;
        const target = btn.getAttribute('data-target');

        // Update tab buttons
        document.querySelectorAll('.tab-switch-btn').forEach(b => {
            b.classList.remove('active');
        });
        btn.classList.add('active');

        // Update tab content
        document.querySelectorAll('.transaction-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        const targetTab = document.querySelector(`.transaction-tab[data-tab="${target}"]`);
        if (targetTab) {
            targetTab.classList.add('active');
        }
    }

    /**
     * Handle category button clicks
     */
    handleCategoryClick(e) {
        const btn = e.currentTarget;
        const category = btn.getAttribute('data-category');

        if (category) {
            this.openTransactionWizard(category);
        }
    }

    /**
     * Open transaction wizard
     */
    openTransactionWizard(categoryId) {
        try {
            if (window.TransactionWizard) {
                window.TransactionWizard.open(categoryId);
            } else {
                throw new Error('Transaction wizard not available');
            }
        } catch (error) {
            console.error('Error opening wizard:', error);
            AppHelpers.showNotification('Lỗi mở wizard: ' + error.message, 'error');
        }
    }

    /**
     * Update company information in header
     */
    updateCompanyInfo() {
        const companyNameEl = document.getElementById('company-name');
        const companyTaxCodeEl = document.getElementById('company-tax-code');

        if (companyNameEl) {
            companyNameEl.textContent = this.companyInfo.name || 'Công ty của bạn';
        }

        if (companyTaxCodeEl) {
            const taxCode = this.companyInfo.taxCode;
            companyTaxCodeEl.textContent = taxCode ? `MST: ${taxCode}` : 'MST: Chưa thiết lập';
        }
    }

    /**
     * Update quick stats in dashboard
     */
    updateQuickStats() {
        const dateRange = this.getCurrentDateRange();
        const filteredTransactions = this.filterTransactionsByDate(this.transactions, dateRange.start, dateRange.end);
        const cashFlow = AppCalculations.calculateCashFlow(filteredTransactions);

        // Update main stats
        const totalIncomeEl = document.getElementById('total-income');
        const totalExpensesEl = document.getElementById('total-expenses');
        const netCashflowEl = document.getElementById('net-cashflow');
        const totalTransactionsEl = document.getElementById('total-transactions');

        if (totalIncomeEl) {
            totalIncomeEl.textContent = AppFormatters.formatCurrency(cashFlow.totalIncome);
        }

        if (totalExpensesEl) {
            totalExpensesEl.textContent = AppFormatters.formatCurrency(cashFlow.totalExpenses);
        }

        if (netCashflowEl) {
            netCashflowEl.textContent = AppFormatters.formatCurrency(cashFlow.netCashFlow);

            // Update color based on positive/negative
            netCashflowEl.className = 'stat-value';
            if (cashFlow.netCashFlow > 0) {
                netCashflowEl.classList.add('text-success');
            } else if (cashFlow.netCashFlow < 0) {
                netCashflowEl.classList.add('text-danger');
            }
        }

        if (totalTransactionsEl) {
            totalTransactionsEl.textContent = cashFlow.transactionCount.toString();
        }

        // Update change indicators (placeholder for now)
        this.updateChangeIndicators(cashFlow);
    }

    /**
     * Get current date range based on period selection
     */
    getCurrentDateRange() {
        const periodSelect = document.getElementById('current-period');
        const period = periodSelect ? periodSelect.value : 'this-month';

        const now = new Date();
        let start, end;

        switch (period) {
            case 'today':
                start = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                end = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
                break;
            case 'this-week':
                const startOfWeek = new Date(now);
                startOfWeek.setDate(now.getDate() - now.getDay());
                start = new Date(startOfWeek.getFullYear(), startOfWeek.getMonth(), startOfWeek.getDate());
                end = new Date(start);
                end.setDate(start.getDate() + 6);
                end.setHours(23, 59, 59);
                break;
            case 'this-month':
                start = new Date(now.getFullYear(), now.getMonth(), 1);
                end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
                break;
            case 'this-quarter':
                const quarter = Math.floor(now.getMonth() / 3);
                start = new Date(now.getFullYear(), quarter * 3, 1);
                end = new Date(now.getFullYear(), quarter * 3 + 3, 0, 23, 59, 59);
                break;
            case 'this-year':
                start = new Date(now.getFullYear(), 0, 1);
                end = new Date(now.getFullYear(), 11, 31, 23, 59, 59);
                break;
            case 'last-month':
                start = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                end = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59);
                break;
            case 'last-quarter':
                const lastQuarter = Math.floor(now.getMonth() / 3) - 1;
                const year = lastQuarter < 0 ? now.getFullYear() - 1 : now.getFullYear();
                const quarterStart = lastQuarter < 0 ? 9 : lastQuarter * 3;
                start = new Date(year, quarterStart, 1);
                end = new Date(year, quarterStart + 3, 0, 23, 59, 59);
                break;
            case 'last-year':
                start = new Date(now.getFullYear() - 1, 0, 1);
                end = new Date(now.getFullYear() - 1, 11, 31, 23, 59, 59);
                break;
            case 'custom':
                const startDateEl = document.getElementById('start-date');
                const endDateEl = document.getElementById('end-date');
                if (startDateEl && endDateEl && startDateEl.value && endDateEl.value) {
                    start = new Date(startDateEl.value);
                    end = new Date(endDateEl.value);
                    end.setHours(23, 59, 59);
                } else {
                    // Default to this month if custom dates not set
                    start = new Date(now.getFullYear(), now.getMonth(), 1);
                    end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
                }
                break;
            default:
                start = new Date(now.getFullYear(), now.getMonth(), 1);
                end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
        }

        return { start, end };
    }

    /**
     * Filter transactions by date range
     */
    filterTransactionsByDate(transactions, startDate, endDate) {
        return transactions.filter(transaction => {
            const transactionDate = new Date(transaction.date || transaction.createdAt);
            return transactionDate >= startDate && transactionDate <= endDate;
        });
    }

    /**
     * Update change indicators (placeholder)
     */
    updateChangeIndicators(currentCashFlow) {
        // This would compare with previous period
        // For now, just show placeholder values
        const incomeChangeEl = document.getElementById('income-change');
        const expenseChangeEl = document.getElementById('expense-change');
        const cashflowChangeEl = document.getElementById('cashflow-change');
        const transactionChangeEl = document.getElementById('transaction-change');

        if (incomeChangeEl) incomeChangeEl.textContent = '+0%';
        if (expenseChangeEl) expenseChangeEl.textContent = '+0%';
        if (cashflowChangeEl) cashflowChangeEl.textContent = '+0%';
        if (transactionChangeEl) transactionChangeEl.textContent = '+0';
    }

    /**
     * Load and display recent transactions
     */
    loadRecentTransactions() {
        const container = document.getElementById('recent-transactions-list');
        if (!container) return;

        // Get recent transactions (last 5)
        const recentTransactions = this.transactions
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);

        if (recentTransactions.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📝</div>
                    <h4>Chưa có giao dịch nào</h4>
                    <p>Bắt đầu bằng cách thêm giao dịch đầu tiên của bạn</p>
                </div>
            `;
            return;
        }

        // Render transaction items
        const transactionHTML = recentTransactions.map(transaction => {
            const category = AppCategories.getById(transaction.category, transaction.type);
            const icon = category?.icon || (transaction.type === 'income' ? '💰' : '💸');
            const amount = transaction.totalAmount || transaction.amount || 0;

            return `
                <div class="transaction-item">
                    <div class="transaction-icon ${transaction.type}">
                        ${icon}
                    </div>
                    <div class="transaction-details">
                        <h5 class="transaction-title">${transaction.description || category?.name || 'Giao dịch'}</h5>
                        <p class="transaction-description">${category?.description || ''}</p>
                    </div>
                    <div class="transaction-amount ${transaction.type}">
                        ${AppFormatters.formatCurrency(amount)}
                    </div>
                    <div class="transaction-date">
                        ${AppFormatters.formatDate(transaction.date || transaction.createdAt)}
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = transactionHTML;
    }

    /**
     * Handle period change
     */
    handlePeriodChange(period) {
        console.log('Period changed to:', period);

        // Show/hide custom date range
        const customDateRange = document.getElementById('custom-date-range');
        if (customDateRange) {
            if (period === 'custom') {
                customDateRange.style.display = 'flex';
            } else {
                customDateRange.style.display = 'none';
            }
        }

        // Update calculations and display based on selected period
        this.updateQuickStats();
        this.loadRecentTransactions();
    }

    /**
     * Handle custom date range change
     */
    handleCustomDateRange() {
        const periodSelect = document.getElementById('current-period');
        if (periodSelect && periodSelect.value === 'custom') {
            this.updateQuickStats();
            this.loadRecentTransactions();
        }
    }

    /**
     * Handle window resize
     */
    handleResize() {
        // Handle responsive behavior
        const sidebar = document.querySelector('.app-sidebar');
        if (AppHelpers.isMobile() && sidebar) {
            sidebar.classList.remove('mobile-open');
        }
    }

    /**
     * Handle keyboard shortcuts
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + shortcuts
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case '1':
                    e.preventDefault();
                    this.switchView('dashboard');
                    break;
                case '2':
                    e.preventDefault();
                    this.switchView('transactions');
                    break;
                case '3':
                    e.preventDefault();
                    this.switchView('reports');
                    break;
                case 's':
                    e.preventDefault();
                    this.showSettingsModal();
                    break;
            }
        }

        // Escape key
        if (e.key === 'Escape') {
            this.hideAllModals();
        }
    }

    /**
     * Show settings modal
     */
    showSettingsModal() {
        const modal = document.getElementById('settings-modal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    /**
     * Hide settings modal
     */
    hideSettingsModal() {
        const modal = document.getElementById('settings-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    /**
     * Hide all modals
     */
    hideAllModals() {
        document.querySelectorAll('.modal-overlay').forEach(modal => {
            modal.style.display = 'none';
        });
    }

    /**
     * Show error message
     */
    showError(message) {
        AppHelpers.showNotification(message, 'error', 5000);
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        AppHelpers.showNotification(message, 'success', 3000);
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Create global app instance
    window.app = new VietnameseAccountingApp();

    // Initialize the application
    window.app.init().catch(error => {
        console.error('Failed to start application:', error);
        document.body.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100vh; flex-direction: column; text-align: center; padding: 20px;">
                <h1 style="color: #dc2626; margin-bottom: 16px;">Lỗi khởi tạo ứng dụng</h1>
                <p style="color: #6b7280; margin-bottom: 24px;">${error.message}</p>
                <button onclick="location.reload()" style="background: #2563eb; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer;">
                    Tải lại trang
                </button>
            </div>
        `;
    });
});

// Handle browser back/forward navigation
window.addEventListener('hashchange', () => {
    if (window.app && window.app.isInitialized) {
        const hash = window.location.hash.substring(1);
        if (hash && ['dashboard', 'transactions', 'reports', 'accounting', 'settings', 'help'].includes(hash)) {
            window.app.switchView(hash);
        }
    }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = VietnameseAccountingApp;
}
