<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ứng dụng Kế toán Thông tư 133 - Quản lý Dòng tiền</title>
    <meta name="description" content="Ứng dụng kế toán tuân thủ Thông tư 133/2016/TT-BTC cho doanh nghiệp nhỏ và vừa">

    <!-- CSS Files -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h2><PERSON><PERSON> tải ứng dụng kế toán...</h2>
            <p><PERSON><PERSON> thủ Thông tư 133/2016/TT-BTC</p>
        </div>
    </div>

    <!-- Main Application -->
    <div id="app" class="app-container" style="display: none;">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="header-left">
                    <h1 class="app-title">
                        <span class="app-icon">📊</span>
                        Kế toán TT 133
                    </h1>
                    <span class="app-subtitle">Quản lý Dòng tiền</span>
                </div>

                <div class="header-center">
                    <div class="period-selector">
                        <label for="current-period">Kỳ kế toán:</label>
                        <select id="current-period" class="period-select">
                            <option value="today">Hôm nay</option>
                            <option value="this-week">Tuần này</option>
                            <option value="this-month" selected>Tháng này</option>
                            <option value="this-quarter">Quý này</option>
                            <option value="this-year">Năm này</option>
                            <option value="last-month">Tháng trước</option>
                            <option value="last-quarter">Quý trước</option>
                            <option value="last-year">Năm trước</option>
                            <option value="custom">Tùy chọn...</option>
                        </select>
                        <div id="custom-date-range" class="custom-date-range" style="display: none;">
                            <input type="date" id="start-date" class="date-input">
                            <span>đến</span>
                            <input type="date" id="end-date" class="date-input">
                        </div>
                    </div>
                </div>

                <div class="header-right">
                    <div class="company-info">
                        <span class="company-name" id="company-name">Công ty của bạn</span>
                        <span class="company-tax-code" id="company-tax-code">MST: Chưa thiết lập</span>
                    </div>
                    <button class="settings-btn" id="settings-btn" title="Cài đặt">
                        ⚙️
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <!-- Sidebar -->
            <aside class="app-sidebar">
                <div class="sidebar-content">
                    <!-- Navigation Menu -->
                    <nav class="sidebar-nav">
                        <h3>Menu chính</h3>
                        <ul class="nav-menu">
                            <li class="nav-item active">
                                <a href="#dashboard" class="nav-link" data-view="dashboard">
                                    <span class="nav-icon">🏠</span>
                                    <span class="nav-text">Trang chủ</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#transactions" class="nav-link" data-view="transactions">
                                    <span class="nav-icon">💳</span>
                                    <span class="nav-text">Giao dịch</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#reports" class="nav-link" data-view="reports">
                                    <span class="nav-icon">📊</span>
                                    <span class="nav-text">Báo cáo</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#accounting" class="nav-link" data-view="accounting">
                                    <span class="nav-icon">📚</span>
                                    <span class="nav-text">Kế toán</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#settings" class="nav-link" data-view="settings">
                                    <span class="nav-icon">⚙️</span>
                                    <span class="nav-text">Cài đặt</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#help" class="nav-link" data-view="help">
                                    <span class="nav-icon">❓</span>
                                    <span class="nav-text">Trợ giúp</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </aside>

            <!-- Content Area -->
            <section class="app-content">
                <!-- Dashboard View -->
                <div id="dashboard-view" class="content-view active">
                    <div class="view-header">
                        <h2>Trang chủ</h2>
                        <p>Tổng quan tài chính doanh nghiệp theo Thông tư 133/2016/TT-BTC</p>
                    </div>

                    <!-- Quick Stats -->
                    <div class="dashboard-stats">
                        <div class="stats-grid">
                            <div class="stat-card income-stat">
                                <div class="stat-icon">💰</div>
                                <div class="stat-content">
                                    <span class="stat-label">Tổng thu nhập</span>
                                    <span class="stat-value" id="total-income">0 ₫</span>
                                    <span class="stat-change positive" id="income-change">+0%</span>
                                </div>
                            </div>

                            <div class="stat-card expense-stat">
                                <div class="stat-icon">💸</div>
                                <div class="stat-content">
                                    <span class="stat-label">Tổng chi phí</span>
                                    <span class="stat-value" id="total-expenses">0 ₫</span>
                                    <span class="stat-change negative" id="expense-change">+0%</span>
                                </div>
                            </div>

                            <div class="stat-card balance-stat">
                                <div class="stat-icon">📈</div>
                                <div class="stat-content">
                                    <span class="stat-label">Dòng tiền ròng</span>
                                    <span class="stat-value" id="net-cashflow">0 ₫</span>
                                    <span class="stat-change" id="cashflow-change">+0%</span>
                                </div>
                            </div>

                            <div class="stat-card transaction-stat">
                                <div class="stat-icon">📊</div>
                                <div class="stat-content">
                                    <span class="stat-label">Số giao dịch</span>
                                    <span class="stat-value" id="total-transactions">0</span>
                                    <span class="stat-change" id="transaction-change">+0</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="quick-actions">
                        <div class="section-header">
                            <h3>Thao tác nhanh</h3>
                            <p>Các chức năng thường dùng</p>
                        </div>
                        <div class="actions-grid">
                            <button class="action-btn primary" onclick="window.app.switchView('transactions')">
                                <div class="action-icon">💳</div>
                                <div class="action-content">
                                    <h4>Thêm giao dịch</h4>
                                    <p>Ghi nhận thu chi mới</p>
                                </div>
                            </button>

                            <button class="action-btn" onclick="window.app.switchView('reports')">
                                <div class="action-icon">📊</div>
                                <div class="action-content">
                                    <h4>Xem báo cáo</h4>
                                    <p>Báo cáo tài chính</p>
                                </div>
                            </button>

                            <button class="action-btn" onclick="window.app.switchView('accounting')">
                                <div class="action-icon">📚</div>
                                <div class="action-content">
                                    <h4>Sổ kế toán</h4>
                                    <p>Bút toán và tài khoản</p>
                                </div>
                            </button>

                            <button class="action-btn" onclick="window.app.switchView('settings')">
                                <div class="action-icon">⚙️</div>
                                <div class="action-content">
                                    <h4>Cài đặt</h4>
                                    <p>Thông tin công ty</p>
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- Recent Transactions -->
                    <div class="recent-transactions">
                        <div class="section-header">
                            <h3>Giao dịch gần đây</h3>
                            <button class="view-all-btn" id="view-all-transactions">
                                Xem tất cả →
                            </button>
                        </div>

                        <div class="transactions-list" id="recent-transactions-list">
                            <div class="empty-state">
                                <div class="empty-icon">📝</div>
                                <h4>Chưa có giao dịch nào</h4>
                                <p>Bắt đầu bằng cách thêm giao dịch đầu tiên của bạn</p>
                            </div>
                        </div>
                    </div>

                    <!-- Financial Summary Chart -->
                    <div class="financial-summary">
                        <div class="section-header">
                            <h3>Biểu đồ tài chính</h3>
                            <p>Xu hướng thu chi theo thời gian</p>
                        </div>
                        <div class="chart-container">
                            <div class="chart-placeholder">
                                <div class="chart-icon">📈</div>
                                <h4>Biểu đồ sẽ hiển thị ở đây</h4>
                                <p>Tính năng này sẽ có trong phiên bản tiếp theo</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Transactions View -->
                <div id="transactions-view" class="content-view">
                    <div class="view-header">
                        <h2>Quản lý giao dịch</h2>
                        <p>Thêm mới và quản lý tất cả giao dịch</p>
                    </div>

                    <!-- Transaction Interface -->
                    <div class="transaction-interface">
                        <div class="interface-header">
                            <h3>Thêm giao dịch mới</h3>
                            <p>Chọn loại giao dịch để bắt đầu quy trình hướng dẫn</p>
                        </div>

                        <div class="transaction-tabs">
                            <!-- Income Tab -->
                            <div class="transaction-tab income-tab active" data-tab="income">
                                <div class="tab-header">
                                    <h4>💰 Thu nhập</h4>
                                    <span class="tab-description">Các khoản thu của doanh nghiệp</span>
                                </div>

                                <div class="category-grid" id="income-categories">
                                    <button class="category-btn primary" data-category="service-revenue">
                                        <div class="category-icon">🏢</div>
                                        <div class="category-content">
                                            <h5>Doanh thu dịch vụ</h5>
                                            <p>Cung cấp dịch vụ cho khách hàng</p>
                                            <span class="vat-info">VAT: 5%</span>
                                        </div>
                                    </button>

                                    <button class="category-btn" data-category="interest-income">
                                        <div class="category-icon">🏦</div>
                                        <div class="category-content">
                                            <h5>Thu nhập từ lãi</h5>
                                            <p>Lãi tiền gửi, cho vay</p>
                                            <span class="vat-info">Không VAT</span>
                                        </div>
                                    </button>

                                    <button class="category-btn" data-category="capital-contribution">
                                        <div class="category-icon">💎</div>
                                        <div class="category-content">
                                            <h5>Góp vốn</h5>
                                            <p>Vốn góp từ chủ sở hữu</p>
                                            <span class="vat-info">Không VAT</span>
                                        </div>
                                    </button>

                                    <button class="category-btn secondary" data-category="other-income">
                                        <div class="category-icon">📋</div>
                                        <div class="category-content">
                                            <h5>Thu nhập khác</h5>
                                            <p>Các khoản thu khác</p>
                                            <span class="vat-info">VAT tùy loại</span>
                                        </div>
                                    </button>
                                </div>
                            </div>

                            <!-- Expense Tab -->
                            <div class="transaction-tab expense-tab" data-tab="expense">
                                <div class="tab-header">
                                    <h4>💸 Chi phí</h4>
                                    <span class="tab-description">Các khoản chi của doanh nghiệp</span>
                                </div>

                                <div class="category-grid" id="expense-categories">
                                    <button class="category-btn primary" data-category="salary-payment">
                                        <div class="category-icon">👥</div>
                                        <div class="category-content">
                                            <h5>Chi trả lương</h5>
                                            <p>Lương, thưởng nhân viên</p>
                                            <span class="vat-info">Không VAT</span>
                                        </div>
                                    </button>

                                    <button class="category-btn" data-category="tax-payment">
                                        <div class="category-icon">🏛️</div>
                                        <div class="category-content">
                                            <h5>Nộp thuế</h5>
                                            <p>Các loại thuế, phí</p>
                                            <span class="vat-info">Không VAT</span>
                                        </div>
                                    </button>

                                    <button class="category-btn" data-category="social-insurance">
                                        <div class="category-icon">🛡️</div>
                                        <div class="category-content">
                                            <h5>Bảo hiểm XH</h5>
                                            <p>BHXH, BHYT, BHTN</p>
                                            <span class="vat-info">Không VAT</span>
                                        </div>
                                    </button>

                                    <button class="category-btn" data-category="capital-withdrawal">
                                        <div class="category-icon">💰</div>
                                        <div class="category-content">
                                            <h5>Rút vốn</h5>
                                            <p>Rút vốn chủ sở hữu</p>
                                            <span class="vat-info">Không VAT</span>
                                        </div>
                                    </button>

                                    <button class="category-btn secondary" data-category="other-expense">
                                        <div class="category-icon">📋</div>
                                        <div class="category-content">
                                            <h5>Chi phí khác</h5>
                                            <p>Các khoản chi khác</p>
                                            <span class="vat-info">VAT tùy loại</span>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Tab Switcher -->
                        <div class="tab-switcher">
                            <button class="tab-switch-btn active" data-target="income">
                                💰 Thu nhập
                            </button>
                            <button class="tab-switch-btn" data-target="expense">
                                💸 Chi phí
                            </button>
                        </div>
                    </div>

                    <!-- All Transactions List -->
                    <div class="all-transactions">
                        <div class="section-header">
                            <h3>Danh sách giao dịch</h3>
                            <div class="transaction-filters">
                                <select id="transaction-filter" class="form-select">
                                    <option value="all">Tất cả</option>
                                    <option value="income">Thu nhập</option>
                                    <option value="expense">Chi phí</option>
                                </select>
                                <input type="text" id="transaction-search" class="form-input" placeholder="Tìm kiếm...">
                            </div>
                        </div>

                        <div class="transactions-table" id="all-transactions-list">
                            <div class="empty-state">
                                <div class="empty-icon">📝</div>
                                <h4>Chưa có giao dịch nào</h4>
                                <p>Thêm giao dịch đầu tiên để bắt đầu</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reports View -->
                <div id="reports-view" class="content-view">
                    <div class="view-header">
                        <h2>Báo cáo tài chính</h2>
                        <p>Báo cáo theo Thông tư 133/2016/TT-BTC</p>
                    </div>
                    <div class="coming-soon">
                        <h3>Đang phát triển...</h3>
                        <p>Tính năng này sẽ có trong phiên bản tiếp theo</p>
                    </div>
                </div>

                <!-- Accounting View -->
                <div id="accounting-view" class="content-view">
                    <div class="view-header">
                        <h2>Sổ kế toán</h2>
                        <p>Bút toán và hệ thống tài khoản theo TT 133</p>
                    </div>
                    <div class="coming-soon">
                        <h3>Đang phát triển...</h3>
                        <p>Tính năng này sẽ có trong phiên bản tiếp theo</p>
                    </div>
                </div>

                <!-- Settings View -->
                <div id="settings-view" class="content-view">
                    <div class="view-header">
                        <h2>Cài đặt hệ thống</h2>
                        <p>Thông tin công ty và cấu hình ứng dụng</p>
                    </div>
                    <div class="coming-soon">
                        <h3>Đang phát triển...</h3>
                        <p>Tính năng này sẽ có trong phiên bản tiếp theo</p>
                    </div>
                </div>

                <!-- Help View -->
                <div id="help-view" class="content-view">
                    <div class="view-header">
                        <h2>Trợ giúp & Hướng dẫn</h2>
                        <p>Hướng dẫn sử dụng và giải thích công thức tính toán</p>
                    </div>
                    <div class="coming-soon">
                        <h3>Đang phát triển...</h3>
                        <p>Tính năng này sẽ có trong phiên bản tiếp theo</p>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Wizard Modal (will be populated by JavaScript) -->
    <div id="wizard-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content wizard-modal">
            <!-- Wizard content will be dynamically generated -->
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settings-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content settings-modal">
            <div class="modal-header">
                <h3>Cài đặt ứng dụng</h3>
                <button class="modal-close" id="settings-close">×</button>
            </div>
            <div class="modal-body">
                <div class="coming-soon">
                    <h4>Đang phát triển...</h4>
                    <p>Tính năng cài đặt sẽ có trong phiên bản tiếp theo</p>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="js/utils/helpers.js"></script>
    <script src="js/utils/formatters.js"></script>
    <script src="js/utils/validators.js"></script>
    <script src="js/data/storage.js"></script>
    <script src="js/data/categories.js"></script>
    <script src="js/data/calculations.js"></script>
    <script src="js/components/wizard.js"></script>
    <script src="js/components/cashflow.js"></script>
    <script src="js/components/categories.js"></script>
    <script src="js/components/validation.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
