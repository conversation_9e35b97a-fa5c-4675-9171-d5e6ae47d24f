/**
 * Vietnamese Accounting App - Transaction Categories Management
 * <PERSON>ân thủ Thông tư 133/2016/TT-BTC
 */

// Transaction categories management for Vietnamese accounting application
const AppCategories = {
    /**
     * Get all transaction categories
     * @returns {object} Categories object with income and expense arrays
     */
    getAll() {
        return AppStorage.getCategories();
    },

    /**
     * Get income categories
     * @returns {Array} Array of income categories
     */
    getIncomeCategories() {
        const categories = this.getAll();
        return categories.income || [];
    },

    /**
     * Get expense categories
     * @returns {Array} Array of expense categories
     */
    getExpenseCategories() {
        const categories = this.getAll();
        return categories.expense || [];
    },

    /**
     * Get category by ID
     * @param {string} categoryId - Category ID
     * @param {string} type - Category type ('income' or 'expense')
     * @returns {object|null} Category object or null if not found
     */
    getById(categoryId, type = null) {
        const categories = this.getAll();
        
        if (type) {
            const categoryList = categories[type] || [];
            return categoryList.find(cat => cat.id === categoryId) || null;
        }
        
        // Search in both income and expense if type not specified
        const incomeCategory = categories.income?.find(cat => cat.id === categoryId);
        if (incomeCategory) return { ...incomeCategory, type: 'income' };
        
        const expenseCategory = categories.expense?.find(cat => cat.id === categoryId);
        if (expenseCategory) return { ...expenseCategory, type: 'expense' };
        
        return null;
    },

    /**
     * Get category details with Vietnamese accounting information
     * @param {string} categoryId - Category ID
     * @returns {object|null} Detailed category information
     */
    getCategoryDetails(categoryId) {
        const category = this.getById(categoryId);
        if (!category) return null;

        // Add detailed information based on Vietnamese accounting standards
        const details = {
            ...category,
            accountingInfo: this.getAccountingInfo(categoryId),
            taxInfo: this.getTaxInfo(categoryId),
            wizardSteps: this.getWizardSteps(categoryId)
        };

        return details;
    },

    /**
     * Get accounting information for a category
     * @param {string} categoryId - Category ID
     * @returns {object} Accounting information
     */
    getAccountingInfo(categoryId) {
        const accountingMap = {
            'service-revenue': {
                debitAccount: '111', // Tiền mặt
                creditAccount: '511', // Doanh thu bán hàng và cung cấp dịch vụ
                vatAccount: '3331', // Thuế GTGT phải nộp
                description: 'Ghi nhận doanh thu từ cung cấp dịch vụ',
                formula: 'Doanh thu = Số lượng × Đơn giá; VAT = Doanh thu × 5%'
            },
            'interest-income': {
                debitAccount: '111', // Tiền mặt
                creditAccount: '515', // Doanh thu hoạt động tài chính
                vatAccount: null,
                description: 'Ghi nhận thu nhập từ lãi tiền gửi, cho vay',
                formula: 'Thu nhập lãi = Số tiền gốc × Lãi suất × Thời gian'
            },
            'capital-contribution': {
                debitAccount: '111', // Tiền mặt
                creditAccount: '411', // Vốn góp của chủ sở hữu
                vatAccount: null,
                description: 'Ghi nhận vốn góp từ chủ sở hữu',
                formula: 'Vốn góp = Số tiền thực tế nhận được'
            },
            'other-income': {
                debitAccount: '111', // Tiền mặt
                creditAccount: '711', // Thu nhập khác
                vatAccount: '3331', // Thuế GTGT phải nộp (nếu có)
                description: 'Ghi nhận các khoản thu nhập khác',
                formula: 'Thu nhập = Số tiền thực tế; VAT tùy theo loại thu nhập'
            },
            'salary-payment': {
                debitAccount: '622', // Chi phí nhân công trực tiếp
                creditAccount: '111', // Tiền mặt
                vatAccount: null,
                description: 'Ghi nhận chi phí lương, thưởng nhân viên',
                formula: 'Lương thực lĩnh = Lương cơ bản + Phụ cấp - Các khoản khấu trừ'
            },
            'tax-payment': {
                debitAccount: '333', // Thuế và các khoản phải nộp nhà nước
                creditAccount: '111', // Tiền mặt
                vatAccount: null,
                description: 'Ghi nhận việc nộp thuế cho nhà nước',
                formula: 'Thuế phải nộp theo quy định của pháp luật'
            },
            'social-insurance': {
                debitAccount: '334', // Phải trả người lao động
                creditAccount: '111', // Tiền mặt
                vatAccount: null,
                description: 'Ghi nhận đóng bảo hiểm xã hội',
                formula: 'BHXH = Lương đóng BHXH × Tỷ lệ đóng'
            },
            'capital-withdrawal': {
                debitAccount: '421', // Lợi nhuận sau thuế chưa phân phối
                creditAccount: '111', // Tiền mặt
                vatAccount: null,
                description: 'Ghi nhận việc rút vốn của chủ sở hữu',
                formula: 'Số tiền rút không vượt quá vốn chủ sở hữu'
            },
            'other-expense': {
                debitAccount: '811', // Chi phí khác
                creditAccount: '111', // Tiền mặt
                vatAccount: '1331', // Thuế GTGT được khấu trừ (nếu có)
                description: 'Ghi nhận các khoản chi phí khác',
                formula: 'Chi phí thực tế phát sinh; VAT tùy theo loại chi phí'
            }
        };

        return accountingMap[categoryId] || {
            debitAccount: null,
            creditAccount: null,
            vatAccount: null,
            description: 'Thông tin kế toán chưa được định nghĩa',
            formula: 'Chưa có công thức tính toán'
        };
    },

    /**
     * Get tax information for a category
     * @param {string} categoryId - Category ID
     * @returns {object} Tax information
     */
    getTaxInfo(categoryId) {
        const taxMap = {
            'service-revenue': {
                vatApplicable: true,
                defaultVATRate: 5,
                vatExemptions: ['Dịch vụ giáo dục', 'Dịch vụ y tế', 'Dịch vụ tài chính'],
                corporateIncomeTax: true,
                personalIncomeTax: false,
                notes: 'Dịch vụ chịu thuế VAT 5% theo quy định'
            },
            'interest-income': {
                vatApplicable: false,
                defaultVATRate: 0,
                vatExemptions: [],
                corporateIncomeTax: true,
                personalIncomeTax: true,
                notes: 'Thu nhập từ lãi không chịu thuế VAT'
            },
            'capital-contribution': {
                vatApplicable: false,
                defaultVATRate: 0,
                vatExemptions: [],
                corporateIncomeTax: false,
                personalIncomeTax: false,
                notes: 'Góp vốn không chịu thuế'
            },
            'other-income': {
                vatApplicable: null, // Depends on specific type
                defaultVATRate: null,
                vatExemptions: [],
                corporateIncomeTax: true,
                personalIncomeTax: false,
                notes: 'Thuế VAT tùy thuộc vào loại thu nhập cụ thể'
            },
            'salary-payment': {
                vatApplicable: false,
                defaultVATRate: 0,
                vatExemptions: [],
                corporateIncomeTax: false,
                personalIncomeTax: true,
                notes: 'Lương chịu thuế TNCN, không chịu VAT'
            },
            'tax-payment': {
                vatApplicable: false,
                defaultVATRate: 0,
                vatExemptions: [],
                corporateIncomeTax: false,
                personalIncomeTax: false,
                notes: 'Nộp thuế không chịu thuế VAT'
            },
            'social-insurance': {
                vatApplicable: false,
                defaultVATRate: 0,
                vatExemptions: [],
                corporateIncomeTax: false,
                personalIncomeTax: false,
                notes: 'Bảo hiểm xã hội không chịu thuế VAT'
            },
            'capital-withdrawal': {
                vatApplicable: false,
                defaultVATRate: 0,
                vatExemptions: [],
                corporateIncomeTax: false,
                personalIncomeTax: false,
                notes: 'Rút vốn không chịu thuế'
            },
            'other-expense': {
                vatApplicable: null, // Depends on specific type
                defaultVATRate: null,
                vatExemptions: [],
                corporateIncomeTax: false,
                personalIncomeTax: false,
                notes: 'Thuế VAT tùy thuộc vào loại chi phí cụ thể'
            }
        };

        return taxMap[categoryId] || {
            vatApplicable: null,
            defaultVATRate: null,
            vatExemptions: [],
            corporateIncomeTax: false,
            personalIncomeTax: false,
            notes: 'Thông tin thuế chưa được định nghĩa'
        };
    },

    /**
     * Get wizard steps for a category
     * @param {string} categoryId - Category ID
     * @returns {Array} Array of wizard steps
     */
    getWizardSteps(categoryId) {
        const wizardMap = {
            'service-revenue': [
                {
                    id: 'customer-info',
                    title: 'Thông tin khách hàng',
                    description: 'Nhập thông tin khách hàng và hợp đồng',
                    fields: [
                        { name: 'customerName', label: 'Tên khách hàng', type: 'text', required: true },
                        { name: 'customerTaxCode', label: 'Mã số thuế', type: 'text', required: false },
                        { name: 'customerAddress', label: 'Địa chỉ', type: 'text', required: false },
                        { name: 'contractNumber', label: 'Số hợp đồng', type: 'text', required: false }
                    ]
                },
                {
                    id: 'service-details',
                    title: 'Chi tiết dịch vụ',
                    description: 'Mô tả dịch vụ và tính toán giá trị',
                    fields: [
                        { name: 'serviceDescription', label: 'Mô tả dịch vụ', type: 'textarea', required: true },
                        { name: 'quantity', label: 'Số lượng', type: 'number', required: true },
                        { name: 'unitPrice', label: 'Đơn giá', type: 'currency', required: true },
                        { name: 'unit', label: 'Đơn vị tính', type: 'text', required: false }
                    ]
                },
                {
                    id: 'tax-payment',
                    title: 'Thuế và thanh toán',
                    description: 'Tính toán thuế VAT và thông tin thanh toán',
                    fields: [
                        { name: 'vatRate', label: 'Thuế suất VAT (%)', type: 'number', required: true, default: 5 },
                        { name: 'paymentMethod', label: 'Phương thức thanh toán', type: 'select', required: true },
                        { name: 'paymentDate', label: 'Ngày thanh toán', type: 'date', required: true },
                        { name: 'invoiceNumber', label: 'Số hóa đơn', type: 'text', required: false }
                    ]
                },
                {
                    id: 'confirmation',
                    title: 'Xác nhận',
                    description: 'Kiểm tra và xác nhận thông tin giao dịch',
                    fields: [
                        { name: 'notes', label: 'Ghi chú', type: 'textarea', required: false },
                        { name: 'attachments', label: 'Tệp đính kèm', type: 'file', required: false }
                    ]
                }
            ],
            'salary-payment': [
                {
                    id: 'employee-info',
                    title: 'Thông tin nhân viên',
                    description: 'Chọn nhân viên và thông tin cơ bản',
                    fields: [
                        { name: 'employeeName', label: 'Tên nhân viên', type: 'text', required: true },
                        { name: 'employeeId', label: 'Mã nhân viên', type: 'text', required: false },
                        { name: 'department', label: 'Phòng ban', type: 'text', required: false },
                        { name: 'position', label: 'Chức vụ', type: 'text', required: false }
                    ]
                },
                {
                    id: 'salary-details',
                    title: 'Chi tiết lương',
                    description: 'Tính toán lương cơ bản và phụ cấp',
                    fields: [
                        { name: 'basicSalary', label: 'Lương cơ bản', type: 'currency', required: true },
                        { name: 'allowances', label: 'Phụ cấp', type: 'currency', required: false },
                        { name: 'overtime', label: 'Làm thêm giờ', type: 'currency', required: false },
                        { name: 'bonus', label: 'Thưởng', type: 'currency', required: false }
                    ]
                },
                {
                    id: 'deductions',
                    title: 'Các khoản khấu trừ',
                    description: 'Tính toán thuế TNCN và các khoản khấu trừ',
                    fields: [
                        { name: 'personalIncomeTax', label: 'Thuế TNCN', type: 'currency', required: false },
                        { name: 'socialInsurance', label: 'BHXH (8%)', type: 'currency', required: false },
                        { name: 'healthInsurance', label: 'BHYT (1.5%)', type: 'currency', required: false },
                        { name: 'unemploymentInsurance', label: 'BHTN (1%)', type: 'currency', required: false },
                        { name: 'otherDeductions', label: 'Khấu trừ khác', type: 'currency', required: false }
                    ]
                },
                {
                    id: 'payment-info',
                    title: 'Thông tin thanh toán',
                    description: 'Phương thức và thời gian thanh toán',
                    fields: [
                        { name: 'paymentMethod', label: 'Phương thức thanh toán', type: 'select', required: true },
                        { name: 'paymentDate', label: 'Ngày thanh toán', type: 'date', required: true },
                        { name: 'bankAccount', label: 'Số tài khoản', type: 'text', required: false },
                        { name: 'notes', label: 'Ghi chú', type: 'textarea', required: false }
                    ]
                }
            ]
            // Add more wizard configurations for other categories...
        };

        return wizardMap[categoryId] || [
            {
                id: 'basic-info',
                title: 'Thông tin cơ bản',
                description: 'Nhập thông tin giao dịch',
                fields: [
                    { name: 'description', label: 'Mô tả', type: 'textarea', required: true },
                    { name: 'amount', label: 'Số tiền', type: 'currency', required: true },
                    { name: 'date', label: 'Ngày giao dịch', type: 'date', required: true },
                    { name: 'notes', label: 'Ghi chú', type: 'textarea', required: false }
                ]
            }
        ];
    },

    /**
     * Get payment methods for dropdowns
     * @returns {Array} Array of payment methods
     */
    getPaymentMethods() {
        return [
            { value: 'cash', label: 'Tiền mặt' },
            { value: 'bank_transfer', label: 'Chuyển khoản ngân hàng' },
            { value: 'check', label: 'Séc' },
            { value: 'card', label: 'Thẻ' },
            { value: 'other', label: 'Khác' }
        ];
    },

    /**
     * Get units of measurement
     * @returns {Array} Array of units
     */
    getUnits() {
        return [
            { value: 'service', label: 'Dịch vụ' },
            { value: 'hour', label: 'Giờ' },
            { value: 'day', label: 'Ngày' },
            { value: 'month', label: 'Tháng' },
            { value: 'project', label: 'Dự án' },
            { value: 'piece', label: 'Cái' },
            { value: 'other', label: 'Khác' }
        ];
    },

    /**
     * Validate category data
     * @param {object} categoryData - Category data to validate
     * @returns {object} Validation result
     */
    validateCategory(categoryData) {
        const errors = {};

        if (!categoryData.id) {
            errors.id = 'ID danh mục không được để trống';
        }

        if (!categoryData.name) {
            errors.name = 'Tên danh mục không được để trống';
        }

        if (categoryData.vatRate !== null && categoryData.vatRate !== undefined) {
            if (typeof categoryData.vatRate !== 'number' || categoryData.vatRate < 0 || categoryData.vatRate > 100) {
                errors.vatRate = 'Thuế suất VAT phải từ 0% đến 100%';
            }
        }

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AppCategories;
} else if (typeof window !== 'undefined') {
    window.AppCategories = AppCategories;
}
