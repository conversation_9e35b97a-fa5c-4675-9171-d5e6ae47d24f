# Ứng dụng Kế toán Việt Nam - Thông tư 133

Ứng dụng kế toán web tuân thủ Thông tư 133/2016/TT-BTC dành cho doanh nghiệp nhỏ và vừa tại Việt Nam.

## 🎯 Mục tiêu dự án

Xây dựng ứng dụng kế toán web đơn giản, dễ sử dụng cho các doanh nghiệp dịch vụ nhỏ tại Việt Nam, tuân thủ đầy đủ các quy định kế toán theo Thông tư 133/2016/TT-BTC.

## ✨ Tính năng chính

### Phase 1: Quản lý Dòng tiền Cơ bản (<PERSON><PERSON> hoàn thành)
- ✅ Giao diện wizard hướng dẫn từng bước
- ✅ Quản lý thu nhập và chi phí theo danh mục
- ✅ Tính toán VAT tự động (5% cho dịch vụ)
- ✅ Lưu trữ dữ liệu local storage
- ✅ Báo cáo dòng tiền cơ bản
- ✅ Giao diện responsive cho mobile

### Phase 2: <PERSON>ế toán & Thuế (Đang phát triển)
- 🔄 Bút toán kế toán tự động
- 🔄 Tính toán thuế TNDN, TNCN
- 🔄 Tính toán bảo hiểm xã hội
- 🔄 Hệ thống tài khoản theo TT 133

### Phase 3: Báo cáo & Phân tích (Kế hoạch)
- 📋 Bảng cân đối kế toán
- 📋 Báo cáo kết quả kinh doanh
- 📋 Báo cáo lưu chuyển tiền tệ
- 📋 Tờ khai thuế

### Phase 4: Tích hợp Google Sheets (Kế hoạch)
- 📋 Đồng bộ dữ liệu với Google Sheets
- 📋 Xuất/nhập dữ liệu
- 📋 Chia sẻ và cộng tác

## 🏗️ Kiến trúc ứng dụng

### Công nghệ sử dụng
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Lưu trữ**: LocalStorage (Phase 1), Google Sheets API (Phase 4)
- **UI Framework**: Custom CSS với responsive design
- **Ngôn ngữ**: Tiếng Việt

### Cấu trúc thư mục
```
app_ke_toan_133/
├── index.html              # Trang chính
├── css/
│   ├── main.css           # Styles chính
│   ├── components.css     # Styles components
│   └── responsive.css     # Responsive design
├── js/
│   ├── app.js            # Controller chính
│   ├── utils/            # Utilities
│   │   ├── helpers.js    # Helper functions
│   │   ├── formatters.js # Định dạng số/tiền
│   │   └── validators.js # Validation
│   ├── data/             # Quản lý dữ liệu
│   │   ├── storage.js    # LocalStorage
│   │   ├── categories.js # Danh mục giao dịch
│   │   └── calculations.js # Tính toán tài chính
│   └── components/       # UI Components
│       ├── wizard.js     # Transaction wizard
│       ├── cashflow.js   # Cash flow management
│       └── validation.js # Form validation
├── data/
│   └── categories.json   # Danh mục mặc định
└── docs/
    ├── calculations.md   # Tài liệu công thức
    └── user-guide.md    # Hướng dẫn sử dụng
```

## 🚀 Cách sử dụng

### Cài đặt
1. Clone hoặc download project
2. Mở file `index.html` trong trình duyệt web
3. Ứng dụng sẽ tự động khởi tạo dữ liệu mặc định

### Thêm giao dịch mới
1. Chọn tab "Thu nhập" hoặc "Chi phí"
2. Click vào danh mục giao dịch phù hợp
3. Làm theo wizard hướng dẫn từng bước
4. Kiểm tra tính toán tự động
5. Hoàn thành và lưu giao dịch

### Xem báo cáo
- Thống kê nhanh hiển thị ở sidebar trái
- Giao dịch gần đây ở trang chủ
- Báo cáo chi tiết (đang phát triển)

## 📊 Danh mục giao dịch

### Thu nhập
- **Doanh thu dịch vụ** (VAT 5%)
- **Thu nhập từ lãi** (Không VAT)
- **Góp vốn** (Không VAT)
- **Thu nhập khác** (VAT tùy loại)

### Chi phí
- **Chi trả lương** (Không VAT)
- **Nộp thuế** (Không VAT)
- **Bảo hiểm XH** (Không VAT)
- **Rút vốn** (Không VAT)
- **Chi phí khác** (VAT tùy loại)

## 🧮 Công thức tính toán

### VAT (Thuế GTGT)
```
VAT = Giá trị chưa thuế × Thuế suất VAT
Tổng tiền = Giá trị chưa thuế + VAT
```

### Doanh thu dịch vụ
```
Thành tiền = Số lượng × Đơn giá
VAT = Thành tiền × 5%
Tổng cộng = Thành tiền + VAT
```

### Lương thực lĩnh
```
Lương gốp = Lương cơ bản + Phụ cấp + Làm thêm
Tổng khấu trừ = TNCN + BHXH + BHYT + BHTN + Khác
Lương thực lĩnh = Lương gốp - Tổng khấu trừ
```

### Bảo hiểm xã hội
```
BHXH = Lương đóng BHXH × 8%
BHYT = Lương đóng BHYT × 1.5%
BHTN = Lương đóng BHTN × 1%
```

## 🔧 Tính năng kỹ thuật

### Validation
- Mã số thuế Việt Nam (10-13 chữ số)
- Số điện thoại Việt Nam
- Định dạng ngày DD/MM/YYYY
- Số tiền và tỷ lệ phần trăm
- Email và các trường bắt buộc

### Định dạng
- Tiền tệ VND với dấu phân cách
- Ngày tháng theo chuẩn Việt Nam
- Số điện thoại và mã số thuế
- Tỷ lệ phần trăm

### Lưu trữ
- LocalStorage cho dữ liệu offline
- Tự động backup định kỳ
- Import/export dữ liệu
- Quản lý phiên bản dữ liệu

## 📱 Responsive Design

Ứng dụng được thiết kế responsive cho:
- **Desktop**: Giao diện đầy đủ với sidebar
- **Tablet**: Layout tối ưu cho màn hình vừa
- **Mobile**: Giao diện thu gọn, navigation ẩn

## 🔒 Bảo mật & Riêng tư

- Dữ liệu lưu trữ hoàn toàn local
- Không gửi thông tin lên server
- Mã hóa dữ liệu nhạy cảm (kế hoạch)
- Backup tự động an toàn

## 🤝 Đóng góp

Dự án này được phát triển để hỗ trợ cộng đồng doanh nghiệp nhỏ Việt Nam. Mọi đóng góp và phản hồi đều được hoan nghênh.

### Cách đóng góp
1. Fork project
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Tạo Pull Request

## 📄 Giấy phép

Dự án này được phát hành dưới giấy phép MIT. Xem file LICENSE để biết thêm chi tiết.

## 📞 Liên hệ

Nếu có thắc mắc hoặc cần hỗ trợ, vui lòng tạo issue trên GitHub hoặc liên hệ qua email.

---

**Lưu ý**: Ứng dụng này được phát triển để hỗ trợ việc quản lý tài chính cơ bản. Đối với các vấn đề kế toán phức tạp, vui lòng tham khảo ý kiến chuyên gia kế toán.
