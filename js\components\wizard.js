/**
 * Vietnamese Accounting App - Transaction Wizard Component
 * <PERSON><PERSON> thủ Thông tư 133/2016/TT-BTC
 */

// Transaction wizard component for guided data entry
class TransactionWizard {
    constructor() {
        this.currentStep = 0;
        this.steps = [];
        this.data = {};
        this.categoryId = null;
        this.categoryType = null;
        this.isOpen = false;
        
        // Bind methods
        this.open = this.open.bind(this);
        this.close = this.close.bind(this);
        this.nextStep = this.nextStep.bind(this);
        this.prevStep = this.prevStep.bind(this);
        this.handleSubmit = this.handleSubmit.bind(this);
        this.validateCurrentStep = this.validateCurrentStep.bind(this);
    }

    /**
     * Open wizard for a specific category
     * @param {string} categoryId - Category ID
     */
    open(categoryId) {
        try {
            // Get category details
            const category = AppCategories.getById(categoryId);
            if (!category) {
                throw new Error('Không tìm thấy danh mục giao dịch');
            }

            this.categoryId = categoryId;
            this.categoryType = category.type;
            this.currentStep = 0;
            this.data = {};

            // Get wizard steps for this category
            this.steps = AppCategories.getWizardSteps(categoryId);
            
            // Initialize data with default values
            this.initializeData();
            
            // Render wizard
            this.render();
            
            // Show modal
            this.showModal();
            
            this.isOpen = true;
            
        } catch (error) {
            console.error('Error opening wizard:', error);
            AppHelpers.showNotification('Lỗi mở wizard: ' + error.message, 'error');
        }
    }

    /**
     * Close wizard
     */
    close() {
        this.hideModal();
        this.isOpen = false;
        this.currentStep = 0;
        this.data = {};
        this.categoryId = null;
        this.categoryType = null;
    }

    /**
     * Initialize data with default values
     */
    initializeData() {
        const category = AppCategories.getById(this.categoryId);
        const taxInfo = AppCategories.getTaxInfo(this.categoryId);
        
        // Set default values
        this.data = {
            categoryId: this.categoryId,
            type: this.categoryType,
            date: AppHelpers.getCurrentDate(),
            vatRate: taxInfo.defaultVATRate || 0,
            paymentMethod: 'cash',
            ...this.data
        };
    }

    /**
     * Render wizard content
     */
    render() {
        const modal = document.getElementById('wizard-modal');
        if (!modal) return;

        const category = AppCategories.getById(this.categoryId);
        const currentStepData = this.steps[this.currentStep];
        
        if (!currentStepData) return;

        const modalContent = modal.querySelector('.modal-content');
        modalContent.innerHTML = `
            <div class="modal-header">
                <h3>${category.name} - ${currentStepData.title}</h3>
                <button class="modal-close" id="wizard-close">×</button>
            </div>
            
            <div class="modal-body">
                <!-- Progress indicator -->
                <div class="wizard-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${((this.currentStep + 1) / this.steps.length) * 100}%"></div>
                    </div>
                    <div class="progress-text">
                        Bước ${this.currentStep + 1} / ${this.steps.length}
                    </div>
                </div>

                <!-- Step description -->
                <div class="step-description">
                    <p>${currentStepData.description}</p>
                </div>

                <!-- Form fields -->
                <form id="wizard-form" class="wizard-form">
                    ${this.renderFields(currentStepData.fields)}
                </form>

                <!-- Calculation preview (if applicable) -->
                ${this.renderCalculationPreview()}

                <!-- Navigation buttons -->
                <div class="wizard-navigation">
                    <button type="button" class="btn btn-secondary" id="wizard-prev" ${this.currentStep === 0 ? 'disabled' : ''}>
                        ← Quay lại
                    </button>
                    
                    <div class="nav-spacer"></div>
                    
                    ${this.currentStep === this.steps.length - 1 
                        ? '<button type="button" class="btn btn-success" id="wizard-submit">Hoàn thành</button>'
                        : '<button type="button" class="btn btn-primary" id="wizard-next">Tiếp theo →</button>'
                    }
                </div>
            </div>
        `;

        // Setup event listeners for this step
        this.setupStepEventListeners();
        
        // Populate form with existing data
        this.populateForm();
    }

    /**
     * Render form fields
     * @param {Array} fields - Field definitions
     * @returns {string} HTML for fields
     */
    renderFields(fields) {
        return fields.map(field => {
            const value = this.data[field.name] || field.default || '';
            
            switch (field.type) {
                case 'text':
                    return `
                        <div class="form-group">
                            <label class="form-label" for="${field.name}">
                                ${field.label} ${field.required ? '<span class="required">*</span>' : ''}
                            </label>
                            <input type="text" id="${field.name}" name="${field.name}" 
                                   class="form-input" value="${value}" 
                                   ${field.required ? 'required' : ''}>
                        </div>
                    `;
                
                case 'textarea':
                    return `
                        <div class="form-group">
                            <label class="form-label" for="${field.name}">
                                ${field.label} ${field.required ? '<span class="required">*</span>' : ''}
                            </label>
                            <textarea id="${field.name}" name="${field.name}" 
                                      class="form-input form-textarea" 
                                      ${field.required ? 'required' : ''}>${value}</textarea>
                        </div>
                    `;
                
                case 'number':
                    return `
                        <div class="form-group">
                            <label class="form-label" for="${field.name}">
                                ${field.label} ${field.required ? '<span class="required">*</span>' : ''}
                            </label>
                            <input type="number" id="${field.name}" name="${field.name}" 
                                   class="form-input" value="${value}" 
                                   step="0.01" min="0"
                                   ${field.required ? 'required' : ''}>
                        </div>
                    `;
                
                case 'currency':
                    return `
                        <div class="form-group">
                            <label class="form-label" for="${field.name}">
                                ${field.label} ${field.required ? '<span class="required">*</span>' : ''}
                            </label>
                            <div class="currency-input">
                                <input type="text" id="${field.name}" name="${field.name}" 
                                       class="form-input currency-field" value="${value}" 
                                       ${field.required ? 'required' : ''}>
                                <span class="currency-symbol">₫</span>
                            </div>
                        </div>
                    `;
                
                case 'date':
                    return `
                        <div class="form-group">
                            <label class="form-label" for="${field.name}">
                                ${field.label} ${field.required ? '<span class="required">*</span>' : ''}
                            </label>
                            <input type="text" id="${field.name}" name="${field.name}" 
                                   class="form-input date-field" value="${value}" 
                                   placeholder="DD/MM/YYYY"
                                   ${field.required ? 'required' : ''}>
                        </div>
                    `;
                
                case 'select':
                    const options = this.getSelectOptions(field.name);
                    return `
                        <div class="form-group">
                            <label class="form-label" for="${field.name}">
                                ${field.label} ${field.required ? '<span class="required">*</span>' : ''}
                            </label>
                            <select id="${field.name}" name="${field.name}" 
                                    class="form-input form-select" 
                                    ${field.required ? 'required' : ''}>
                                <option value="">-- Chọn --</option>
                                ${options.map(opt => 
                                    `<option value="${opt.value}" ${opt.value === value ? 'selected' : ''}>${opt.label}</option>`
                                ).join('')}
                            </select>
                        </div>
                    `;
                
                case 'file':
                    return `
                        <div class="form-group">
                            <label class="form-label" for="${field.name}">
                                ${field.label} ${field.required ? '<span class="required">*</span>' : ''}
                            </label>
                            <input type="file" id="${field.name}" name="${field.name}" 
                                   class="form-input" multiple accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                            <div class="form-help">Chấp nhận: PDF, JPG, PNG, DOC, DOCX</div>
                        </div>
                    `;
                
                default:
                    return '';
            }
        }).join('');
    }

    /**
     * Get options for select fields
     * @param {string} fieldName - Field name
     * @returns {Array} Options array
     */
    getSelectOptions(fieldName) {
        switch (fieldName) {
            case 'paymentMethod':
                return AppCategories.getPaymentMethods();
            case 'unit':
                return AppCategories.getUnits();
            default:
                return [];
        }
    }

    /**
     * Render calculation preview
     * @returns {string} HTML for calculation preview
     */
    renderCalculationPreview() {
        if (this.currentStep < 1) return ''; // Only show on later steps
        
        const calculations = this.calculateCurrentValues();
        if (!calculations) return '';

        return `
            <div class="calculation-preview">
                <h4>Tính toán tự động</h4>
                <div class="calculation-grid">
                    ${calculations.subtotal !== undefined ? `
                        <div class="calc-item">
                            <span class="calc-label">Thành tiền:</span>
                            <span class="calc-value">${AppFormatters.formatCurrency(calculations.subtotal)}</span>
                        </div>
                    ` : ''}
                    ${calculations.vatAmount !== undefined ? `
                        <div class="calc-item">
                            <span class="calc-label">VAT (${calculations.vatRate}%):</span>
                            <span class="calc-value">${AppFormatters.formatCurrency(calculations.vatAmount)}</span>
                        </div>
                    ` : ''}
                    ${calculations.total !== undefined ? `
                        <div class="calc-item total">
                            <span class="calc-label">Tổng cộng:</span>
                            <span class="calc-value">${AppFormatters.formatCurrency(calculations.total)}</span>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * Calculate current values based on form data
     * @returns {object|null} Calculation results
     */
    calculateCurrentValues() {
        if (this.categoryId === 'service-revenue') {
            const quantity = parseFloat(this.data.quantity) || 0;
            const unitPrice = AppFormatters.parseCurrency(this.data.unitPrice) || 0;
            const vatRate = parseFloat(this.data.vatRate) || 0;
            
            if (quantity > 0 && unitPrice > 0) {
                return AppCalculations.calculateServiceRevenue(quantity, unitPrice, vatRate);
            }
        } else if (this.categoryId === 'salary-payment') {
            return AppCalculations.calculateSalary(this.data);
        }
        
        return null;
    }

    /**
     * Setup event listeners for current step
     */
    setupStepEventListeners() {
        // Close button
        const closeBtn = document.getElementById('wizard-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', this.close);
        }

        // Navigation buttons
        const prevBtn = document.getElementById('wizard-prev');
        const nextBtn = document.getElementById('wizard-next');
        const submitBtn = document.getElementById('wizard-submit');

        if (prevBtn) {
            prevBtn.addEventListener('click', this.prevStep);
        }

        if (nextBtn) {
            nextBtn.addEventListener('click', this.nextStep);
        }

        if (submitBtn) {
            submitBtn.addEventListener('click', this.handleSubmit);
        }

        // Form field listeners for real-time calculation
        const form = document.getElementById('wizard-form');
        if (form) {
            form.addEventListener('input', AppHelpers.debounce(() => {
                this.updateDataFromForm();
                this.updateCalculationPreview();
            }, 300));
        }

        // Currency field formatting
        document.querySelectorAll('.currency-field').forEach(field => {
            field.addEventListener('blur', (e) => {
                const value = AppFormatters.parseCurrency(e.target.value);
                e.target.value = AppFormatters.formatCurrency(value, false);
            });
        });
    }

    /**
     * Populate form with existing data
     */
    populateForm() {
        Object.keys(this.data).forEach(key => {
            const field = document.getElementById(key);
            if (field && this.data[key] !== undefined) {
                if (field.type === 'checkbox') {
                    field.checked = this.data[key];
                } else {
                    field.value = this.data[key];
                }
            }
        });
    }

    /**
     * Update data from form
     */
    updateDataFromForm() {
        const form = document.getElementById('wizard-form');
        if (!form) return;

        const formData = new FormData(form);
        for (const [key, value] of formData.entries()) {
            this.data[key] = value;
        }
    }

    /**
     * Update calculation preview
     */
    updateCalculationPreview() {
        const preview = document.querySelector('.calculation-preview');
        if (preview) {
            const newPreview = this.renderCalculationPreview();
            if (newPreview) {
                preview.outerHTML = newPreview;
            }
        }
    }

    /**
     * Validate current step
     * @returns {object} Validation result
     */
    validateCurrentStep() {
        this.updateDataFromForm();
        
        const currentStepData = this.steps[this.currentStep];
        const errors = {};
        
        currentStepData.fields.forEach(field => {
            if (field.required && !this.data[field.name]) {
                errors[field.name] = `${field.label} không được để trống`;
            }
            
            // Additional validation based on field type
            if (this.data[field.name]) {
                switch (field.type) {
                    case 'currency':
                        const amount = AppFormatters.parseCurrency(this.data[field.name]);
                        if (isNaN(amount) || amount < 0) {
                            errors[field.name] = `${field.label} phải là số tiền hợp lệ`;
                        }
                        break;
                    case 'number':
                        const num = parseFloat(this.data[field.name]);
                        if (isNaN(num) || num < 0) {
                            errors[field.name] = `${field.label} phải là số hợp lệ`;
                        }
                        break;
                    case 'date':
                        const dateValidation = AppValidators.validateDate(this.data[field.name]);
                        if (!dateValidation.isValid) {
                            errors[field.name] = dateValidation.message;
                        }
                        break;
                }
            }
        });
        
        // Display errors
        this.displayErrors(errors);
        
        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }

    /**
     * Display validation errors
     * @param {object} errors - Errors object
     */
    displayErrors(errors) {
        // Clear previous errors
        document.querySelectorAll('.form-error').forEach(el => el.remove());
        document.querySelectorAll('.form-input.error').forEach(el => el.classList.remove('error'));
        
        // Display new errors
        Object.keys(errors).forEach(fieldName => {
            const field = document.getElementById(fieldName);
            if (field) {
                field.classList.add('error');
                
                const errorEl = document.createElement('div');
                errorEl.className = 'form-error';
                errorEl.textContent = errors[fieldName];
                
                field.parentNode.appendChild(errorEl);
            }
        });
    }

    /**
     * Go to next step
     */
    nextStep() {
        const validation = this.validateCurrentStep();
        if (!validation.isValid) {
            AppHelpers.showNotification('Vui lòng kiểm tra và sửa các lỗi', 'warning');
            return;
        }
        
        if (this.currentStep < this.steps.length - 1) {
            this.currentStep++;
            this.render();
        }
    }

    /**
     * Go to previous step
     */
    prevStep() {
        if (this.currentStep > 0) {
            this.currentStep--;
            this.render();
        }
    }

    /**
     * Handle form submission
     */
    async handleSubmit() {
        const validation = this.validateCurrentStep();
        if (!validation.isValid) {
            AppHelpers.showNotification('Vui lòng kiểm tra và sửa các lỗi', 'warning');
            return;
        }

        try {
            // Prepare transaction data
            const transactionData = this.prepareTransactionData();
            
            // Save transaction
            const success = AppStorage.addTransaction(transactionData);
            
            if (success) {
                AppHelpers.showNotification('Giao dịch đã được lưu thành công!', 'success');
                
                // Update app data
                if (window.app) {
                    await window.app.loadData();
                    window.app.updateQuickStats();
                    window.app.loadRecentTransactions();
                }
                
                this.close();
            } else {
                throw new Error('Không thể lưu giao dịch');
            }
            
        } catch (error) {
            console.error('Error submitting transaction:', error);
            AppHelpers.showNotification('Lỗi lưu giao dịch: ' + error.message, 'error');
        }
    }

    /**
     * Prepare transaction data for saving
     * @returns {object} Transaction data
     */
    prepareTransactionData() {
        const category = AppCategories.getById(this.categoryId);
        const calculations = this.calculateCurrentValues();
        
        const baseData = {
            id: AppHelpers.generateId(),
            type: this.categoryType,
            category: this.categoryId,
            categoryName: category.name,
            description: this.data.serviceDescription || this.data.description || category.name,
            date: this.data.date || AppHelpers.getCurrentDate(),
            createdAt: AppHelpers.getCurrentTimestamp(),
            updatedAt: AppHelpers.getCurrentTimestamp(),
            ...this.data
        };

        // Add calculated amounts
        if (calculations) {
            baseData.subtotal = calculations.subtotal;
            baseData.vatAmount = calculations.vatAmount;
            baseData.totalAmount = calculations.total;
            baseData.amount = calculations.total; // For compatibility
        } else {
            // For non-calculated transactions
            const amount = AppFormatters.parseCurrency(this.data.amount) || 0;
            baseData.amount = amount;
            baseData.totalAmount = amount;
        }

        return baseData;
    }

    /**
     * Show modal
     */
    showModal() {
        const modal = document.getElementById('wizard-modal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    /**
     * Hide modal
     */
    hideModal() {
        const modal = document.getElementById('wizard-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }
}

// Create global wizard instance
window.TransactionWizard = new TransactionWizard();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TransactionWizard;
}
