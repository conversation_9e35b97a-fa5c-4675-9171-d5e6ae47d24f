/* ===== COMPONENT STYLES ===== */
/* Transaction Interface, Wizards, Modals, and Interactive Components */

/* Transaction Interface */
.transaction-interface {
    background: var(--white);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
}

.interface-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.interface-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
}

.interface-header p {
    color: var(--gray-600);
    font-size: 1rem;
}

/* Transaction Tabs */
.transaction-tabs {
    position: relative;
}

.transaction-tab {
    display: none;
    padding: var(--spacing-xl);
}

.transaction-tab.active {
    display: block;
}

.tab-header {
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

.tab-header h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.tab-description {
    color: var(--gray-600);
    font-size: 0.875rem;
}

/* Category Grid */
.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

/* Category Buttons */
.category-btn {
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: var(--transition-normal);
    text-align: left;
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    min-height: 120px;
    position: relative;
    overflow: hidden;
}

.category-btn:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.category-btn:active {
    transform: translateY(0);
}

.category-btn.primary {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, var(--white), #f8faff);
}

.category-btn.primary:hover {
    background: linear-gradient(135deg, #f8faff, #f1f5ff);
}

.category-btn.secondary {
    border-color: var(--gray-300);
    background: var(--gray-50);
}

.category-btn.secondary:hover {
    border-color: var(--gray-400);
    background: var(--gray-100);
}

.category-icon {
    font-size: 2rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gray-100);
    border-radius: var(--radius-md);
    flex-shrink: 0;
}

.category-btn.primary .category-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
}

.category-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.category-content h5 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.category-content p {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin: 0;
    line-height: 1.4;
}

.vat-info {
    font-size: 0.75rem;
    font-weight: 600;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    background: var(--gray-100);
    color: var(--gray-700);
    align-self: flex-start;
    margin-top: auto;
}

.category-btn.primary .vat-info {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

/* Tab Switcher */
.tab-switcher {
    display: flex;
    background: var(--gray-100);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xs);
    margin: 0 var(--spacing-xl) var(--spacing-xl);
}

.tab-switch-btn {
    flex: 1;
    background: transparent;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.tab-switch-btn:hover {
    color: var(--gray-800);
}

.tab-switch-btn.active {
    background: var(--white);
    color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

/* Recent Transactions Section */
.recent-transactions {
    background: var(--white);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.section-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.view-all-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.view-all-btn:hover {
    background: var(--primary-color);
    color: var(--white);
}

.transactions-list {
    padding: var(--spacing-xl);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--gray-500);
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.empty-state h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
}

.empty-state p {
    font-size: 0.875rem;
    color: var(--gray-500);
}

/* Dashboard Specific Styles */
.dashboard-stats {
    margin-bottom: var(--spacing-2xl);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.dashboard-stats .stat-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.dashboard-stats .stat-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.dashboard-stats .stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
}

.dashboard-stats .income-stat::before {
    background: var(--income-color);
}

.dashboard-stats .expense-stat::before {
    background: var(--expense-color);
}

.dashboard-stats .balance-stat::before {
    background: var(--primary-color);
}

.dashboard-stats .transaction-stat::before {
    background: var(--info-color);
}

.dashboard-stats .stat-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-lg);
    flex-shrink: 0;
}

.dashboard-stats .income-stat .stat-icon {
    background: var(--income-light);
}

.dashboard-stats .expense-stat .stat-icon {
    background: var(--expense-light);
}

.dashboard-stats .balance-stat .stat-icon {
    background: var(--gray-100);
}

.dashboard-stats .transaction-stat .stat-icon {
    background: rgba(8, 145, 178, 0.1);
}

.dashboard-stats .stat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.dashboard-stats .stat-label {
    font-size: 0.875rem;
    color: var(--gray-500);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.dashboard-stats .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-800);
    line-height: 1.2;
}

.dashboard-stats .income-stat .stat-value {
    color: var(--income-color);
}

.dashboard-stats .expense-stat .stat-value {
    color: var(--expense-color);
}

.stat-change {
    font-size: 0.75rem;
    font-weight: 600;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    display: inline-block;
}

.stat-change.positive {
    background: var(--income-light);
    color: var(--income-color);
}

.stat-change.negative {
    background: var(--expense-light);
    color: var(--expense-color);
}

/* Quick Actions */
.quick-actions {
    margin-bottom: var(--spacing-2xl);
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.action-btn {
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: var(--transition-normal);
    text-align: left;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    min-height: 100px;
}

.action-btn:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.action-btn.primary {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, var(--white), #f8faff);
}

.action-btn.primary:hover {
    background: linear-gradient(135deg, #f8faff, #f1f5ff);
}

.action-icon {
    font-size: 2rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gray-100);
    border-radius: var(--radius-md);
    flex-shrink: 0;
}

.action-btn.primary .action-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
}

.action-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.action-content h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.action-content p {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin: 0;
    line-height: 1.4;
}

/* Financial Summary Chart */
.financial-summary {
    background: var(--white);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.chart-container {
    padding: var(--spacing-xl);
}

.chart-placeholder {
    text-align: center;
    padding: var(--spacing-2xl);
    background: var(--gray-50);
    border-radius: var(--radius-md);
    border: 2px dashed var(--gray-300);
}

.chart-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.chart-placeholder h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
}

.chart-placeholder p {
    font-size: 0.875rem;
    color: var(--gray-500);
}

/* Transaction Filters */
.transaction-filters {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.transaction-filters .form-select,
.transaction-filters .form-input {
    min-width: 150px;
}

/* Custom Date Range */
.custom-date-range {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: var(--gray-50);
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-200);
}

.date-input {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    background: var(--white);
}

.date-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.custom-date-range span {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    padding: var(--spacing-lg);
    backdrop-filter: blur(4px);
}

.modal-content {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.wizard-modal {
    width: 100%;
    max-width: 800px;
}

.settings-modal {
    width: 100%;
    max-width: 600px;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.modal-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

.modal-body {
    padding: var(--spacing-xl);
    overflow-y: auto;
    max-height: calc(90vh - 120px);
}

/* Transaction Item Styles (for future use) */
.transaction-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-200);
    margin-bottom: var(--spacing-md);
    transition: var(--transition-fast);
}

.transaction-item:hover {
    box-shadow: var(--shadow-sm);
    border-color: var(--gray-300);
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.transaction-icon.income {
    background: var(--income-light);
    color: var(--income-color);
}

.transaction-icon.expense {
    background: var(--expense-light);
    color: var(--expense-color);
}

.transaction-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.transaction-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.transaction-description {
    font-size: 0.75rem;
    color: var(--gray-500);
    margin: 0;
}

.transaction-amount {
    font-size: 1rem;
    font-weight: 700;
    text-align: right;
}

.transaction-amount.income {
    color: var(--income-color);
}

.transaction-amount.expense {
    color: var(--expense-color);
}

.transaction-date {
    font-size: 0.75rem;
    color: var(--gray-400);
    text-align: right;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-fast);
    line-height: 1;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--gray-700);
}

.btn-secondary:hover {
    background: var(--gray-200);
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn-success:hover {
    background: #047857;
}

.btn-danger {
    background: var(--danger-color);
    color: var(--white);
}

.btn-danger:hover {
    background: #b91c1c;
}

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.75rem;
}

.btn-lg {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: 1rem;
}

/* Form Elements */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
}

.form-input {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    color: var(--gray-700);
    background: var(--white);
    transition: var(--transition-fast);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-input:invalid {
    border-color: var(--danger-color);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

.form-error {
    font-size: 0.75rem;
    color: var(--danger-color);
    margin-top: var(--spacing-xs);
}

.form-help {
    font-size: 0.75rem;
    color: var(--gray-500);
    margin-top: var(--spacing-xs);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--gray-300);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Wizard Specific Styles */
.wizard-progress {
    margin-bottom: var(--spacing-xl);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    transition: width var(--transition-normal);
}

.progress-text {
    text-align: center;
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

.step-description {
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-md);
    background: var(--gray-50);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary-color);
}

.step-description p {
    margin: 0;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.wizard-form {
    margin-bottom: var(--spacing-xl);
}

.wizard-navigation {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
}

.nav-spacer {
    flex: 1;
}

.currency-input {
    position: relative;
    display: flex;
    align-items: center;
}

.currency-input .form-input {
    padding-right: 40px;
}

.currency-symbol {
    position: absolute;
    right: var(--spacing-md);
    color: var(--gray-500);
    font-weight: 600;
    pointer-events: none;
}

.calculation-preview {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
}

.calculation-preview h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
}

.calculation-grid {
    display: grid;
    gap: var(--spacing-sm);
}

.calc-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
}

.calc-item.total {
    border-top: 1px solid var(--gray-300);
    padding-top: var(--spacing-md);
    margin-top: var(--spacing-sm);
    font-weight: 600;
}

.calc-label {
    color: var(--gray-600);
    font-size: 0.875rem;
}

.calc-value {
    color: var(--gray-800);
    font-weight: 600;
    font-size: 0.875rem;
}

.calc-item.total .calc-label,
.calc-item.total .calc-value {
    color: var(--primary-color);
    font-size: 1rem;
}

.required {
    color: var(--danger-color);
    margin-left: 2px;
}

.form-input.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.date-field {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.25em 1.25em;
    padding-right: 3rem;
}
