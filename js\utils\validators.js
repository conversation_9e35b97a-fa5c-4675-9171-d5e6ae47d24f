/**
 * Vietnamese Accounting App - Input Validation Functions
 * Tuân thủ Thông tư 133/2016/TT-BTC
 */

// Validation utilities for Vietnamese accounting application
const AppValidators = {
    /**
     * Validate Vietnamese tax code (MST)
     * @param {string} taxCode - Tax code to validate
     * @returns {object} Validation result with isValid and message
     */
    validateTaxCode(taxCode) {
        if (!taxCode || typeof taxCode !== 'string') {
            return { isValid: false, message: '<PERSON><PERSON> số thuế không được để trống' };
        }

        // Remove all non-digits
        const digits = taxCode.replace(/\D/g, '');

        // Vietnamese tax codes are 10 or 13 digits
        if (digits.length !== 10 && digits.length !== 13) {
            return { isValid: false, message: 'Mã số thuế phải có 10 hoặc 13 chữ số' };
        }

        // Basic checksum validation for 10-digit tax codes
        if (digits.length === 10) {
            const checksum = this._calculateTaxCodeChecksum(digits.substring(0, 9));
            if (checksum !== parseInt(digits[9])) {
                return { isValid: false, message: '<PERSON>ã số thuế không hợp lệ (sai checksum)' };
            }
        }

        return { isValid: true, message: '' };
    },

    /**
     * Calculate checksum for Vietnamese tax code
     * @private
     * @param {string} digits - First 9 digits of tax code
     * @returns {number} Checksum digit
     */
    _calculateTaxCodeChecksum(digits) {
        const weights = [31, 29, 23, 19, 17, 13, 7, 5, 3];
        let sum = 0;

        for (let i = 0; i < 9; i++) {
            sum += parseInt(digits[i]) * weights[i];
        }

        const remainder = sum % 11;
        return remainder < 2 ? remainder : 11 - remainder;
    },

    /**
     * Validate Vietnamese phone number
     * @param {string} phone - Phone number to validate
     * @returns {object} Validation result
     */
    validatePhoneNumber(phone) {
        if (!phone || typeof phone !== 'string') {
            return { isValid: false, message: 'Số điện thoại không được để trống' };
        }

        // Remove all non-digits
        const digits = phone.replace(/\D/g, '');

        // Vietnamese phone numbers are typically 10 digits
        if (digits.length !== 10) {
            return { isValid: false, message: 'Số điện thoại phải có 10 chữ số' };
        }

        // Mobile numbers start with 03, 05, 07, 08, 09
        // Landline numbers start with 02
        const validPrefixes = ['02', '03', '05', '07', '08', '09'];
        const prefix = digits.substring(0, 2);

        if (!validPrefixes.includes(prefix)) {
            return { isValid: false, message: 'Số điện thoại không đúng định dạng Việt Nam' };
        }

        return { isValid: true, message: '' };
    },

    /**
     * Validate email address
     * @param {string} email - Email to validate
     * @returns {object} Validation result
     */
    validateEmail(email) {
        if (!email || typeof email !== 'string') {
            return { isValid: false, message: 'Email không được để trống' };
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return { isValid: false, message: 'Email không đúng định dạng' };
        }

        if (email.length > 254) {
            return { isValid: false, message: 'Email quá dài (tối đa 254 ký tự)' };
        }

        return { isValid: true, message: '' };
    },

    /**
     * Validate currency amount
     * @param {string|number} amount - Amount to validate
     * @param {number} min - Minimum allowed value
     * @param {number} max - Maximum allowed value
     * @returns {object} Validation result
     */
    validateAmount(amount, min = 0, max = Number.MAX_SAFE_INTEGER) {
        if (amount === null || amount === undefined || amount === '') {
            return { isValid: false, message: 'Số tiền không được để trống' };
        }

        let numericAmount;
        if (typeof amount === 'string') {
            // Parse Vietnamese currency format
            numericAmount = AppFormatters.parseCurrency(amount);
        } else if (typeof amount === 'number') {
            numericAmount = amount;
        } else {
            return { isValid: false, message: 'Số tiền không hợp lệ' };
        }

        if (isNaN(numericAmount)) {
            return { isValid: false, message: 'Số tiền phải là số' };
        }

        if (numericAmount < min) {
            return { isValid: false, message: `Số tiền không được nhỏ hơn ${AppFormatters.formatCurrency(min)}` };
        }

        if (numericAmount > max) {
            return { isValid: false, message: `Số tiền không được lớn hơn ${AppFormatters.formatCurrency(max)}` };
        }

        return { isValid: true, message: '', value: numericAmount };
    },

    /**
     * Validate VAT rate
     * @param {string|number} rate - VAT rate to validate
     * @returns {object} Validation result
     */
    validateVATRate(rate) {
        if (rate === null || rate === undefined || rate === '') {
            return { isValid: false, message: 'Thuế suất VAT không được để trống' };
        }

        const numericRate = typeof rate === 'string' ? parseFloat(rate) : rate;

        if (isNaN(numericRate)) {
            return { isValid: false, message: 'Thuế suất VAT phải là số' };
        }

        if (numericRate < 0 || numericRate > 100) {
            return { isValid: false, message: 'Thuế suất VAT phải từ 0% đến 100%' };
        }

        // Common VAT rates in Vietnam: 0%, 5%, 10%
        const validRates = [0, 5, 8, 10];
        if (!validRates.includes(numericRate)) {
            return { 
                isValid: true, 
                message: 'Cảnh báo: Thuế suất VAT không phổ biến tại Việt Nam',
                warning: true,
                value: numericRate
            };
        }

        return { isValid: true, message: '', value: numericRate };
    },

    /**
     * Validate Vietnamese date format (DD/MM/YYYY)
     * @param {string} dateString - Date string to validate
     * @returns {object} Validation result
     */
    validateDate(dateString) {
        if (!dateString || typeof dateString !== 'string') {
            return { isValid: false, message: 'Ngày không được để trống' };
        }

        const dateRegex = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
        const match = dateString.match(dateRegex);

        if (!match) {
            return { isValid: false, message: 'Ngày phải có định dạng DD/MM/YYYY' };
        }

        const day = parseInt(match[1], 10);
        const month = parseInt(match[2], 10);
        const year = parseInt(match[3], 10);

        // Validate ranges
        if (month < 1 || month > 12) {
            return { isValid: false, message: 'Tháng phải từ 1 đến 12' };
        }

        if (day < 1 || day > 31) {
            return { isValid: false, message: 'Ngày phải từ 1 đến 31' };
        }

        // Create date object to validate actual date
        const date = new Date(year, month - 1, day);
        if (date.getDate() !== day || date.getMonth() !== month - 1 || date.getFullYear() !== year) {
            return { isValid: false, message: 'Ngày không tồn tại' };
        }

        // Check reasonable year range
        const currentYear = new Date().getFullYear();
        if (year < 1900 || year > currentYear + 10) {
            return { isValid: false, message: `Năm phải từ 1900 đến ${currentYear + 10}` };
        }

        return { isValid: true, message: '', value: date };
    },

    /**
     * Validate required field
     * @param {any} value - Value to validate
     * @param {string} fieldName - Name of the field for error message
     * @returns {object} Validation result
     */
    validateRequired(value, fieldName = 'Trường này') {
        if (value === null || value === undefined) {
            return { isValid: false, message: `${fieldName} không được để trống` };
        }

        if (typeof value === 'string' && value.trim() === '') {
            return { isValid: false, message: `${fieldName} không được để trống` };
        }

        if (Array.isArray(value) && value.length === 0) {
            return { isValid: false, message: `${fieldName} phải có ít nhất một mục` };
        }

        return { isValid: true, message: '' };
    },

    /**
     * Validate text length
     * @param {string} text - Text to validate
     * @param {number} minLength - Minimum length
     * @param {number} maxLength - Maximum length
     * @param {string} fieldName - Field name for error message
     * @returns {object} Validation result
     */
    validateTextLength(text, minLength = 0, maxLength = 255, fieldName = 'Trường này') {
        if (typeof text !== 'string') {
            return { isValid: false, message: `${fieldName} phải là văn bản` };
        }

        const trimmedText = text.trim();

        if (trimmedText.length < minLength) {
            return { isValid: false, message: `${fieldName} phải có ít nhất ${minLength} ký tự` };
        }

        if (trimmedText.length > maxLength) {
            return { isValid: false, message: `${fieldName} không được vượt quá ${maxLength} ký tự` };
        }

        return { isValid: true, message: '', value: trimmedText };
    },

    /**
     * Validate business registration number
     * @param {string} regNumber - Registration number to validate
     * @returns {object} Validation result
     */
    validateBusinessRegistration(regNumber) {
        if (!regNumber || typeof regNumber !== 'string') {
            return { isValid: false, message: 'Số đăng ký kinh doanh không được để trống' };
        }

        // Remove all non-digits
        const digits = regNumber.replace(/\D/g, '');

        // Vietnamese business registration numbers are typically 13 digits
        if (digits.length !== 13) {
            return { isValid: false, message: 'Số đăng ký kinh doanh phải có 13 chữ số' };
        }

        return { isValid: true, message: '' };
    },

    /**
     * Validate bank account number
     * @param {string} accountNumber - Account number to validate
     * @returns {object} Validation result
     */
    validateBankAccount(accountNumber) {
        if (!accountNumber || typeof accountNumber !== 'string') {
            return { isValid: false, message: 'Số tài khoản ngân hàng không được để trống' };
        }

        // Remove all non-alphanumeric characters
        const cleaned = accountNumber.replace(/[^a-zA-Z0-9]/g, '');

        // Vietnamese bank account numbers are typically 6-20 characters
        if (cleaned.length < 6 || cleaned.length > 20) {
            return { isValid: false, message: 'Số tài khoản ngân hàng phải có từ 6 đến 20 ký tự' };
        }

        return { isValid: true, message: '' };
    },

    /**
     * Validate percentage value
     * @param {string|number} percentage - Percentage to validate
     * @param {number} min - Minimum percentage
     * @param {number} max - Maximum percentage
     * @returns {object} Validation result
     */
    validatePercentage(percentage, min = 0, max = 100) {
        if (percentage === null || percentage === undefined || percentage === '') {
            return { isValid: false, message: 'Tỷ lệ phần trăm không được để trống' };
        }

        const numericPercentage = typeof percentage === 'string' ? parseFloat(percentage) : percentage;

        if (isNaN(numericPercentage)) {
            return { isValid: false, message: 'Tỷ lệ phần trăm phải là số' };
        }

        if (numericPercentage < min || numericPercentage > max) {
            return { isValid: false, message: `Tỷ lệ phần trăm phải từ ${min}% đến ${max}%` };
        }

        return { isValid: true, message: '', value: numericPercentage };
    },

    /**
     * Validate form data against rules
     * @param {object} data - Form data to validate
     * @param {object} rules - Validation rules
     * @returns {object} Validation result with errors object
     */
    validateForm(data, rules) {
        const errors = {};
        let isValid = true;

        for (const field in rules) {
            const fieldRules = rules[field];
            const value = data[field];
            
            for (const rule of fieldRules) {
                let result;
                
                switch (rule.type) {
                    case 'required':
                        result = this.validateRequired(value, rule.message || field);
                        break;
                    case 'email':
                        if (value) result = this.validateEmail(value);
                        break;
                    case 'phone':
                        if (value) result = this.validatePhoneNumber(value);
                        break;
                    case 'taxCode':
                        if (value) result = this.validateTaxCode(value);
                        break;
                    case 'amount':
                        if (value) result = this.validateAmount(value, rule.min, rule.max);
                        break;
                    case 'date':
                        if (value) result = this.validateDate(value);
                        break;
                    case 'textLength':
                        if (value) result = this.validateTextLength(value, rule.min, rule.max, rule.message || field);
                        break;
                    default:
                        continue;
                }
                
                if (result && !result.isValid) {
                    errors[field] = result.message;
                    isValid = false;
                    break; // Stop at first error for this field
                }
            }
        }

        return { isValid, errors };
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AppValidators;
} else if (typeof window !== 'undefined') {
    window.AppValidators = AppValidators;
}
