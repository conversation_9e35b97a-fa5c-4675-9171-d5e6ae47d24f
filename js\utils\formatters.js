/**
 * Vietnamese Accounting App - Number and Currency Formatters
 * <PERSON><PERSON> thủ Thông tư 133/2016/TT-BTC
 */

// Formatting utilities for Vietnamese accounting application
const AppFormatters = {
    /**
     * Format number as Vietnamese currency (VND)
     * @param {number} amount - Amount to format
     * @param {boolean} showSymbol - Whether to show currency symbol
     * @param {boolean} showDecimals - Whether to show decimal places
     * @returns {string} Formatted currency string
     */
    formatCurrency(amount, showSymbol = true, showDecimals = false) {
        if (typeof amount !== 'number' || isNaN(amount)) {
            return showSymbol ? '0 ₫' : '0';
        }

        // Round to avoid floating point precision issues
        const roundedAmount = Math.round(amount * 100) / 100;
        
        // Format with Vietnamese number formatting
        const options = {
            minimumFractionDigits: showDecimals ? 2 : 0,
            maximumFractionDigits: showDecimals ? 2 : 0,
            useGrouping: true
        };

        // Use Vietnamese locale for number formatting
        let formatted;
        try {
            formatted = new Intl.NumberFormat('vi-VN', options).format(Math.abs(roundedAmount));
        } catch (e) {
            // Fallback for browsers that don't support vi-VN locale
            formatted = Math.abs(roundedAmount).toLocaleString('en-US', options);
            // Replace commas with dots for Vietnamese style
            formatted = formatted.replace(/,/g, '.');
        }

        // Add negative sign if needed
        if (roundedAmount < 0) {
            formatted = '-' + formatted;
        }

        // Add currency symbol
        if (showSymbol) {
            formatted += ' ₫';
        }

        return formatted;
    },

    /**
     * Parse Vietnamese currency string to number
     * @param {string} currencyString - Currency string to parse
     * @returns {number} Parsed number or 0 if invalid
     */
    parseCurrency(currencyString) {
        if (typeof currencyString !== 'string') return 0;

        // Remove currency symbol and whitespace
        let cleaned = currencyString.replace(/[₫\s]/g, '');
        
        // Handle negative numbers
        const isNegative = cleaned.startsWith('-');
        if (isNegative) {
            cleaned = cleaned.substring(1);
        }

        // Replace Vietnamese decimal separators
        cleaned = cleaned.replace(/\./g, '').replace(/,/g, '.');

        const parsed = parseFloat(cleaned);
        if (isNaN(parsed)) return 0;

        return isNegative ? -parsed : parsed;
    },

    /**
     * Format number with Vietnamese thousand separators
     * @param {number} number - Number to format
     * @param {number} decimals - Number of decimal places
     * @returns {string} Formatted number string
     */
    formatNumber(number, decimals = 0) {
        if (typeof number !== 'number' || isNaN(number)) return '0';

        const options = {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals,
            useGrouping: true
        };

        try {
            return new Intl.NumberFormat('vi-VN', options).format(number);
        } catch (e) {
            // Fallback formatting
            const formatted = number.toFixed(decimals);
            return formatted.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
        }
    },

    /**
     * Format percentage with Vietnamese formatting
     * @param {number} value - Value to format as percentage (0.05 = 5%)
     * @param {number} decimals - Number of decimal places
     * @returns {string} Formatted percentage string
     */
    formatPercentage(value, decimals = 1) {
        if (typeof value !== 'number' || isNaN(value)) return '0%';

        const percentage = value * 100;
        const formatted = this.formatNumber(percentage, decimals);
        return formatted + '%';
    },

    /**
     * Format VAT rate for display
     * @param {number} rate - VAT rate (5 for 5%)
     * @returns {string} Formatted VAT rate
     */
    formatVATRate(rate) {
        if (typeof rate !== 'number' || isNaN(rate)) return '0%';
        
        if (rate === 0) return 'Không VAT';
        return this.formatNumber(rate, 0) + '%';
    },

    /**
     * Format Vietnamese tax code (MST)
     * @param {string} taxCode - Tax code to format
     * @returns {string} Formatted tax code
     */
    formatTaxCode(taxCode) {
        if (typeof taxCode !== 'string') return '';
        
        // Remove all non-digits
        const digits = taxCode.replace(/\D/g, '');
        
        // Vietnamese tax codes are typically 10 or 13 digits
        if (digits.length === 10) {
            // Format as XXXX-XXX-XXX
            return digits.replace(/(\d{4})(\d{3})(\d{3})/, '$1-$2-$3');
        } else if (digits.length === 13) {
            // Format as XXXX-XXX-XXX-XXX
            return digits.replace(/(\d{4})(\d{3})(\d{3})(\d{3})/, '$1-$2-$3-$4');
        }
        
        return digits;
    },

    /**
     * Format Vietnamese phone number
     * @param {string} phone - Phone number to format
     * @returns {string} Formatted phone number
     */
    formatPhoneNumber(phone) {
        if (typeof phone !== 'string') return '';
        
        // Remove all non-digits
        const digits = phone.replace(/\D/g, '');
        
        // Vietnamese mobile numbers (10 digits starting with 0)
        if (digits.length === 10 && digits.startsWith('0')) {
            return digits.replace(/(\d{4})(\d{3})(\d{3})/, '$1 $2 $3');
        }
        
        // Vietnamese landline numbers (10 digits)
        if (digits.length === 10) {
            return digits.replace(/(\d{2})(\d{4})(\d{4})/, '$1 $2 $3');
        }
        
        return digits;
    },

    /**
     * Format Vietnamese address
     * @param {object} address - Address object with components
     * @returns {string} Formatted address string
     */
    formatAddress(address) {
        if (!address || typeof address !== 'object') return '';
        
        const parts = [];
        
        if (address.street) parts.push(address.street);
        if (address.ward) parts.push(address.ward);
        if (address.district) parts.push(address.district);
        if (address.city) parts.push(address.city);
        if (address.province) parts.push(address.province);
        
        return parts.join(', ');
    },

    /**
     * Format date in Vietnamese format (DD/MM/YYYY)
     * @param {Date|string} date - Date to format
     * @returns {string} Formatted date string
     */
    formatDate(date) {
        if (!date) return '';
        
        let dateObj;
        if (typeof date === 'string') {
            dateObj = new Date(date);
        } else if (date instanceof Date) {
            dateObj = date;
        } else {
            return '';
        }
        
        if (isNaN(dateObj.getTime())) return '';
        
        const day = String(dateObj.getDate()).padStart(2, '0');
        const month = String(dateObj.getMonth() + 1).padStart(2, '0');
        const year = dateObj.getFullYear();
        
        return `${day}/${month}/${year}`;
    },

    /**
     * Format datetime in Vietnamese format
     * @param {Date|string} date - Date to format
     * @param {boolean} includeSeconds - Whether to include seconds
     * @returns {string} Formatted datetime string
     */
    formatDateTime(date, includeSeconds = false) {
        if (!date) return '';
        
        let dateObj;
        if (typeof date === 'string') {
            dateObj = new Date(date);
        } else if (date instanceof Date) {
            dateObj = date;
        } else {
            return '';
        }
        
        if (isNaN(dateObj.getTime())) return '';
        
        const dateStr = this.formatDate(dateObj);
        const hours = String(dateObj.getHours()).padStart(2, '0');
        const minutes = String(dateObj.getMinutes()).padStart(2, '0');
        
        let timeStr = `${hours}:${minutes}`;
        
        if (includeSeconds) {
            const seconds = String(dateObj.getSeconds()).padStart(2, '0');
            timeStr += `:${seconds}`;
        }
        
        return `${dateStr} ${timeStr}`;
    },

    /**
     * Format time duration in Vietnamese
     * @param {number} milliseconds - Duration in milliseconds
     * @returns {string} Formatted duration string
     */
    formatDuration(milliseconds) {
        if (typeof milliseconds !== 'number' || milliseconds < 0) return '0 giây';
        
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        
        if (days > 0) {
            return `${days} ngày`;
        } else if (hours > 0) {
            return `${hours} giờ`;
        } else if (minutes > 0) {
            return `${minutes} phút`;
        } else {
            return `${seconds} giây`;
        }
    },

    /**
     * Format file size in Vietnamese
     * @param {number} bytes - File size in bytes
     * @returns {string} Formatted file size
     */
    formatFileSize(bytes) {
        if (typeof bytes !== 'number' || bytes < 0) return '0 Bytes';
        
        const units = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        let size = bytes;
        let unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        const formatted = unitIndex === 0 ? size.toString() : size.toFixed(1);
        return `${formatted} ${units[unitIndex]}`;
    },

    /**
     * Format transaction reference number
     * @param {string} id - Transaction ID
     * @param {string} prefix - Prefix for reference
     * @returns {string} Formatted reference number
     */
    formatTransactionReference(id, prefix = 'GD') {
        if (typeof id !== 'string') return '';
        
        // Extract timestamp and random part from ID
        const parts = id.split('_');
        if (parts.length >= 2) {
            const timestamp = parts[1];
            const random = parts[2] || '';
            
            // Create a shorter reference
            const shortTimestamp = timestamp.slice(-8); // Last 8 digits
            const shortRandom = random.slice(0, 4); // First 4 characters
            
            return `${prefix}${shortTimestamp}${shortRandom}`.toUpperCase();
        }
        
        return `${prefix}${id.slice(-8)}`.toUpperCase();
    },

    /**
     * Format account number for display
     * @param {string} accountNumber - Account number
     * @returns {string} Formatted account number
     */
    formatAccountNumber(accountNumber) {
        if (typeof accountNumber !== 'string') return '';
        
        // Remove all non-alphanumeric characters
        const cleaned = accountNumber.replace(/[^a-zA-Z0-9]/g, '');
        
        // Group digits for better readability
        if (cleaned.length > 8) {
            return cleaned.replace(/(.{4})/g, '$1 ').trim();
        }
        
        return cleaned;
    },

    /**
     * Truncate text with ellipsis
     * @param {string} text - Text to truncate
     * @param {number} maxLength - Maximum length
     * @returns {string} Truncated text
     */
    truncateText(text, maxLength = 50) {
        if (typeof text !== 'string') return '';
        
        if (text.length <= maxLength) return text;
        
        return text.substring(0, maxLength - 3) + '...';
    },

    /**
     * Format business registration number
     * @param {string} regNumber - Registration number
     * @returns {string} Formatted registration number
     */
    formatBusinessRegistration(regNumber) {
        if (typeof regNumber !== 'string') return '';
        
        // Remove all non-digits
        const digits = regNumber.replace(/\D/g, '');
        
        // Vietnamese business registration numbers are typically 13 digits
        if (digits.length === 13) {
            return digits.replace(/(\d{4})(\d{3})(\d{3})(\d{3})/, '$1-$2-$3-$4');
        }
        
        return digits;
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AppFormatters;
} else if (typeof window !== 'undefined') {
    window.AppFormatters = AppFormatters;
}
