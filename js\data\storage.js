/**
 * Vietnamese Accounting App - Local Storage Management
 * <PERSON><PERSON> thủ Thông tư 133/2016/TT-BTC
 */

// Local storage management for Vietnamese accounting application
const AppStorage = {
    // Storage keys
    KEYS: {
        TRANSACTIONS: 'vn_accounting_transactions',
        COMPANY_INFO: 'vn_accounting_company',
        SETTINGS: 'vn_accounting_settings',
        CATEGORIES: 'vn_accounting_categories',
        BACKUP: 'vn_accounting_backup',
        VERSION: 'vn_accounting_version'
    },

    // Current data version for migration purposes
    CURRENT_VERSION: '1.0.0',

    /**
     * Initialize storage with default data
     */
    init() {
        try {
            // Check if this is first run
            const version = this.get(this.KEYS.VERSION);
            if (!version) {
                this.initializeDefaults();
                this.set(this.KEYS.VERSION, this.CURRENT_VERSION);
            } else if (version !== this.CURRENT_VERSION) {
                this.migrateData(version, this.CURRENT_VERSION);
            }
        } catch (error) {
            console.error('Storage initialization failed:', error);
            AppHelpers.showNotification('Lỗi khởi tạo dữ liệu', 'error');
        }
    },

    /**
     * Initialize default data for first run
     */
    initializeDefaults() {
        // Default company information
        const defaultCompany = {
            name: 'Công ty của bạn',
            taxCode: '',
            address: '',
            phone: '',
            email: '',
            representative: '',
            businessType: 'service',
            createdAt: AppHelpers.getCurrentTimestamp()
        };

        // Default settings
        const defaultSettings = {
            currency: 'VND',
            dateFormat: 'DD/MM/YYYY',
            defaultVATRate: 5,
            fiscalYearStart: '01/01',
            language: 'vi',
            theme: 'light',
            autoBackup: true,
            backupFrequency: 'daily',
            createdAt: AppHelpers.getCurrentTimestamp()
        };

        // Initialize with defaults
        this.set(this.KEYS.COMPANY_INFO, defaultCompany);
        this.set(this.KEYS.SETTINGS, defaultSettings);
        this.set(this.KEYS.TRANSACTIONS, []);
        this.set(this.KEYS.CATEGORIES, this.getDefaultCategories());
    },

    /**
     * Get default transaction categories
     * @returns {object} Default categories
     */
    getDefaultCategories() {
        return {
            income: [
                {
                    id: 'service-revenue',
                    name: 'Doanh thu dịch vụ',
                    description: 'Cung cấp dịch vụ cho khách hàng',
                    vatRate: 5,
                    accountCode: '511',
                    icon: '🏢'
                },
                {
                    id: 'interest-income',
                    name: 'Thu nhập từ lãi',
                    description: 'Lãi tiền gửi, cho vay',
                    vatRate: 0,
                    accountCode: '515',
                    icon: '🏦'
                },
                {
                    id: 'capital-contribution',
                    name: 'Góp vốn',
                    description: 'Vốn góp từ chủ sở hữu',
                    vatRate: 0,
                    accountCode: '411',
                    icon: '💎'
                },
                {
                    id: 'other-income',
                    name: 'Thu nhập khác',
                    description: 'Các khoản thu khác',
                    vatRate: null,
                    accountCode: '711',
                    icon: '📋'
                }
            ],
            expense: [
                {
                    id: 'salary-payment',
                    name: 'Chi trả lương',
                    description: 'Lương, thưởng nhân viên',
                    vatRate: 0,
                    accountCode: '622',
                    icon: '👥'
                },
                {
                    id: 'tax-payment',
                    name: 'Nộp thuế',
                    description: 'Các loại thuế, phí',
                    vatRate: 0,
                    accountCode: '333',
                    icon: '🏛️'
                },
                {
                    id: 'social-insurance',
                    name: 'Bảo hiểm XH',
                    description: 'BHXH, BHYT, BHTN',
                    vatRate: 0,
                    accountCode: '334',
                    icon: '🛡️'
                },
                {
                    id: 'capital-withdrawal',
                    name: 'Rút vốn',
                    description: 'Rút vốn chủ sở hữu',
                    vatRate: 0,
                    accountCode: '421',
                    icon: '💰'
                },
                {
                    id: 'other-expense',
                    name: 'Chi phí khác',
                    description: 'Các khoản chi khác',
                    vatRate: null,
                    accountCode: '811',
                    icon: '📋'
                }
            ]
        };
    },

    /**
     * Get data from localStorage
     * @param {string} key - Storage key
     * @param {any} defaultValue - Default value if key doesn't exist
     * @returns {any} Stored data or default value
     */
    get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            if (item === null) return defaultValue;
            return JSON.parse(item);
        } catch (error) {
            console.error(`Error getting ${key} from storage:`, error);
            return defaultValue;
        }
    },

    /**
     * Set data in localStorage
     * @param {string} key - Storage key
     * @param {any} value - Value to store
     * @returns {boolean} Success status
     */
    set(key, value) {
        try {
            const serialized = JSON.stringify(value);
            localStorage.setItem(key, serialized);
            return true;
        } catch (error) {
            console.error(`Error setting ${key} in storage:`, error);
            AppHelpers.showNotification('Lỗi lưu dữ liệu', 'error');
            return false;
        }
    },

    /**
     * Remove data from localStorage
     * @param {string} key - Storage key
     * @returns {boolean} Success status
     */
    remove(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error(`Error removing ${key} from storage:`, error);
            return false;
        }
    },

    /**
     * Clear all application data
     * @returns {boolean} Success status
     */
    clear() {
        try {
            Object.values(this.KEYS).forEach(key => {
                localStorage.removeItem(key);
            });
            return true;
        } catch (error) {
            console.error('Error clearing storage:', error);
            return false;
        }
    },

    /**
     * Get all transactions
     * @returns {Array} Array of transactions
     */
    getTransactions() {
        return this.get(this.KEYS.TRANSACTIONS, []);
    },

    /**
     * Save transactions
     * @param {Array} transactions - Array of transactions
     * @returns {boolean} Success status
     */
    setTransactions(transactions) {
        return this.set(this.KEYS.TRANSACTIONS, transactions);
    },

    /**
     * Add a new transaction
     * @param {object} transaction - Transaction object
     * @returns {boolean} Success status
     */
    addTransaction(transaction) {
        const transactions = this.getTransactions();
        transactions.push({
            ...transaction,
            id: transaction.id || AppHelpers.generateId(),
            createdAt: transaction.createdAt || AppHelpers.getCurrentTimestamp(),
            updatedAt: AppHelpers.getCurrentTimestamp()
        });
        return this.setTransactions(transactions);
    },

    /**
     * Update an existing transaction
     * @param {string} id - Transaction ID
     * @param {object} updates - Updates to apply
     * @returns {boolean} Success status
     */
    updateTransaction(id, updates) {
        const transactions = this.getTransactions();
        const index = transactions.findIndex(t => t.id === id);
        
        if (index === -1) return false;
        
        transactions[index] = {
            ...transactions[index],
            ...updates,
            updatedAt: AppHelpers.getCurrentTimestamp()
        };
        
        return this.setTransactions(transactions);
    },

    /**
     * Delete a transaction
     * @param {string} id - Transaction ID
     * @returns {boolean} Success status
     */
    deleteTransaction(id) {
        const transactions = this.getTransactions();
        const filtered = transactions.filter(t => t.id !== id);
        return this.setTransactions(filtered);
    },

    /**
     * Get company information
     * @returns {object} Company information
     */
    getCompanyInfo() {
        return this.get(this.KEYS.COMPANY_INFO, {});
    },

    /**
     * Update company information
     * @param {object} companyInfo - Company information
     * @returns {boolean} Success status
     */
    setCompanyInfo(companyInfo) {
        const existing = this.getCompanyInfo();
        const updated = {
            ...existing,
            ...companyInfo,
            updatedAt: AppHelpers.getCurrentTimestamp()
        };
        return this.set(this.KEYS.COMPANY_INFO, updated);
    },

    /**
     * Get application settings
     * @returns {object} Settings object
     */
    getSettings() {
        return this.get(this.KEYS.SETTINGS, {});
    },

    /**
     * Update application settings
     * @param {object} settings - Settings to update
     * @returns {boolean} Success status
     */
    setSettings(settings) {
        const existing = this.getSettings();
        const updated = {
            ...existing,
            ...settings,
            updatedAt: AppHelpers.getCurrentTimestamp()
        };
        return this.set(this.KEYS.SETTINGS, updated);
    },

    /**
     * Get transaction categories
     * @returns {object} Categories object
     */
    getCategories() {
        return this.get(this.KEYS.CATEGORIES, this.getDefaultCategories());
    },

    /**
     * Update transaction categories
     * @param {object} categories - Categories object
     * @returns {boolean} Success status
     */
    setCategories(categories) {
        return this.set(this.KEYS.CATEGORIES, categories);
    },

    /**
     * Export all data for backup
     * @returns {object} All application data
     */
    exportData() {
        return {
            version: this.CURRENT_VERSION,
            exportDate: AppHelpers.getCurrentTimestamp(),
            data: {
                transactions: this.getTransactions(),
                company: this.getCompanyInfo(),
                settings: this.getSettings(),
                categories: this.getCategories()
            }
        };
    },

    /**
     * Import data from backup
     * @param {object} backupData - Backup data object
     * @returns {boolean} Success status
     */
    importData(backupData) {
        try {
            if (!backupData || !backupData.data) {
                throw new Error('Invalid backup data format');
            }

            const { data } = backupData;
            
            // Validate data structure
            if (data.transactions && Array.isArray(data.transactions)) {
                this.setTransactions(data.transactions);
            }
            
            if (data.company && typeof data.company === 'object') {
                this.setCompanyInfo(data.company);
            }
            
            if (data.settings && typeof data.settings === 'object') {
                this.setSettings(data.settings);
            }
            
            if (data.categories && typeof data.categories === 'object') {
                this.setCategories(data.categories);
            }

            // Update version
            this.set(this.KEYS.VERSION, backupData.version || this.CURRENT_VERSION);
            
            return true;
        } catch (error) {
            console.error('Error importing data:', error);
            AppHelpers.showNotification('Lỗi nhập dữ liệu: ' + error.message, 'error');
            return false;
        }
    },

    /**
     * Create automatic backup
     * @returns {boolean} Success status
     */
    createBackup() {
        try {
            const backupData = this.exportData();
            const backupKey = `${this.KEYS.BACKUP}_${Date.now()}`;
            
            // Keep only last 5 backups
            const existingBackups = Object.keys(localStorage)
                .filter(key => key.startsWith(this.KEYS.BACKUP))
                .sort();
            
            if (existingBackups.length >= 5) {
                // Remove oldest backups
                existingBackups.slice(0, -4).forEach(key => {
                    localStorage.removeItem(key);
                });
            }
            
            return this.set(backupKey, backupData);
        } catch (error) {
            console.error('Error creating backup:', error);
            return false;
        }
    },

    /**
     * Get storage usage information
     * @returns {object} Storage usage stats
     */
    getStorageInfo() {
        try {
            let totalSize = 0;
            const itemSizes = {};
            
            Object.entries(this.KEYS).forEach(([name, key]) => {
                const item = localStorage.getItem(key);
                const size = item ? new Blob([item]).size : 0;
                itemSizes[name] = size;
                totalSize += size;
            });
            
            // Estimate available space (5MB typical limit)
            const estimatedLimit = 5 * 1024 * 1024; // 5MB
            const usagePercentage = (totalSize / estimatedLimit) * 100;
            
            return {
                totalSize,
                itemSizes,
                usagePercentage: Math.min(usagePercentage, 100),
                estimatedLimit
            };
        } catch (error) {
            console.error('Error getting storage info:', error);
            return null;
        }
    },

    /**
     * Migrate data between versions
     * @param {string} fromVersion - Current version
     * @param {string} toVersion - Target version
     */
    migrateData(fromVersion, toVersion) {
        console.log(`Migrating data from ${fromVersion} to ${toVersion}`);
        
        // Add migration logic here when needed
        // For now, just update the version
        this.set(this.KEYS.VERSION, toVersion);
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AppStorage;
} else if (typeof window !== 'undefined') {
    window.AppStorage = AppStorage;
}
