/**
 * Vietnamese Accounting App - Financial Calculations
 * Tuân thủ Thông tư 133/2016/TT-BTC
 */

// Financial calculation utilities for Vietnamese accounting application
const AppCalculations = {
    /**
     * Calculate VAT amount
     * @param {number} amount - Base amount (excluding VAT)
     * @param {number} vatRate - VAT rate as percentage (e.g., 5 for 5%)
     * @returns {object} Calculation result with breakdown
     */
    calculateVAT(amount, vatRate = 5) {
        if (typeof amount !== 'number' || typeof vatRate !== 'number') {
            return { subtotal: 0, vatAmount: 0, total: 0, vatRate: 0 };
        }

        const subtotal = Math.round(amount * 100) / 100;
        const vatAmount = Math.round(subtotal * (vatRate / 100) * 100) / 100;
        const total = Math.round((subtotal + vatAmount) * 100) / 100;

        return {
            subtotal,
            vatAmount,
            total,
            vatRate,
            formula: `VAT = ${AppFormatters.formatCurrency(subtotal)} × ${vatRate}% = ${AppFormatters.formatCurrency(vatAmount)}`
        };
    },

    /**
     * Calculate VAT from total amount (VAT inclusive)
     * @param {number} totalAmount - Total amount including VAT
     * @param {number} vatRate - VAT rate as percentage
     * @returns {object} Calculation result with breakdown
     */
    calculateVATFromTotal(totalAmount, vatRate = 5) {
        if (typeof totalAmount !== 'number' || typeof vatRate !== 'number') {
            return { subtotal: 0, vatAmount: 0, total: 0, vatRate: 0 };
        }

        const total = Math.round(totalAmount * 100) / 100;
        const subtotal = Math.round((total / (1 + vatRate / 100)) * 100) / 100;
        const vatAmount = Math.round((total - subtotal) * 100) / 100;

        return {
            subtotal,
            vatAmount,
            total,
            vatRate,
            formula: `Subtotal = ${AppFormatters.formatCurrency(total)} ÷ (1 + ${vatRate}%) = ${AppFormatters.formatCurrency(subtotal)}`
        };
    },

    /**
     * Calculate service revenue with VAT
     * @param {number} quantity - Quantity of service
     * @param {number} unitPrice - Unit price per service
     * @param {number} vatRate - VAT rate
     * @returns {object} Detailed calculation
     */
    calculateServiceRevenue(quantity, unitPrice, vatRate = 5) {
        if (typeof quantity !== 'number' || typeof unitPrice !== 'number') {
            return { quantity: 0, unitPrice: 0, subtotal: 0, vatAmount: 0, total: 0 };
        }

        const subtotal = Math.round(quantity * unitPrice * 100) / 100;
        const vatCalculation = this.calculateVAT(subtotal, vatRate);

        return {
            quantity,
            unitPrice,
            ...vatCalculation,
            formula: `Subtotal = ${quantity} × ${AppFormatters.formatCurrency(unitPrice)} = ${AppFormatters.formatCurrency(subtotal)}\n${vatCalculation.formula}`
        };
    },

    /**
     * Calculate salary with deductions (Vietnamese system)
     * @param {object} salaryData - Salary components
     * @returns {object} Detailed salary calculation
     */
    calculateSalary(salaryData) {
        const {
            basicSalary = 0,
            allowances = 0,
            overtime = 0,
            bonus = 0,
            personalIncomeTax = 0,
            socialInsurance = 0,
            healthInsurance = 0,
            unemploymentInsurance = 0,
            otherDeductions = 0
        } = salaryData;

        // Calculate gross salary
        const grossSalary = Math.round((basicSalary + allowances + overtime + bonus) * 100) / 100;

        // Calculate total deductions
        const totalDeductions = Math.round((
            personalIncomeTax + 
            socialInsurance + 
            healthInsurance + 
            unemploymentInsurance + 
            otherDeductions
        ) * 100) / 100;

        // Calculate net salary
        const netSalary = Math.round((grossSalary - totalDeductions) * 100) / 100;

        return {
            basicSalary,
            allowances,
            overtime,
            bonus,
            grossSalary,
            deductions: {
                personalIncomeTax,
                socialInsurance,
                healthInsurance,
                unemploymentInsurance,
                otherDeductions,
                total: totalDeductions
            },
            netSalary,
            formula: `Gross = ${AppFormatters.formatCurrency(basicSalary)} + ${AppFormatters.formatCurrency(allowances)} + ${AppFormatters.formatCurrency(overtime)} + ${AppFormatters.formatCurrency(bonus)} = ${AppFormatters.formatCurrency(grossSalary)}\nNet = ${AppFormatters.formatCurrency(grossSalary)} - ${AppFormatters.formatCurrency(totalDeductions)} = ${AppFormatters.formatCurrency(netSalary)}`
        };
    },

    /**
     * Calculate Vietnamese social insurance contributions
     * @param {number} baseSalary - Base salary for insurance calculation
     * @returns {object} Insurance calculation breakdown
     */
    calculateSocialInsurance(baseSalary) {
        if (typeof baseSalary !== 'number' || baseSalary <= 0) {
            return { baseSalary: 0, socialInsurance: 0, healthInsurance: 0, unemploymentInsurance: 0, total: 0 };
        }

        // Vietnamese social insurance rates (employee portion)
        const socialInsuranceRate = 0.08; // 8%
        const healthInsuranceRate = 0.015; // 1.5%
        const unemploymentInsuranceRate = 0.01; // 1%

        const socialInsurance = Math.round(baseSalary * socialInsuranceRate * 100) / 100;
        const healthInsurance = Math.round(baseSalary * healthInsuranceRate * 100) / 100;
        const unemploymentInsurance = Math.round(baseSalary * unemploymentInsuranceRate * 100) / 100;
        const total = Math.round((socialInsurance + healthInsurance + unemploymentInsurance) * 100) / 100;

        return {
            baseSalary,
            socialInsurance,
            healthInsurance,
            unemploymentInsurance,
            total,
            rates: {
                socialInsurance: socialInsuranceRate * 100,
                healthInsurance: healthInsuranceRate * 100,
                unemploymentInsurance: unemploymentInsuranceRate * 100
            },
            formula: `BHXH = ${AppFormatters.formatCurrency(baseSalary)} × 8% = ${AppFormatters.formatCurrency(socialInsurance)}\nBHYT = ${AppFormatters.formatCurrency(baseSalary)} × 1.5% = ${AppFormatters.formatCurrency(healthInsurance)}\nBHTN = ${AppFormatters.formatCurrency(baseSalary)} × 1% = ${AppFormatters.formatCurrency(unemploymentInsurance)}`
        };
    },

    /**
     * Calculate Vietnamese personal income tax (simplified)
     * @param {number} taxableIncome - Monthly taxable income
     * @returns {object} Tax calculation
     */
    calculatePersonalIncomeTax(taxableIncome) {
        if (typeof taxableIncome !== 'number' || taxableIncome <= 0) {
            return { taxableIncome: 0, tax: 0, effectiveRate: 0 };
        }

        // Vietnamese personal income tax brackets (2024)
        const brackets = [
            { min: 0, max: 5000000, rate: 0.05 },
            { min: 5000000, max: 10000000, rate: 0.10 },
            { min: 10000000, max: 18000000, rate: 0.15 },
            { min: 18000000, max: 32000000, rate: 0.20 },
            { min: 32000000, max: 52000000, rate: 0.25 },
            { min: 52000000, max: 80000000, rate: 0.30 },
            { min: 80000000, max: Infinity, rate: 0.35 }
        ];

        let tax = 0;
        let remainingIncome = taxableIncome;

        for (const bracket of brackets) {
            if (remainingIncome <= 0) break;

            const taxableAtThisBracket = Math.min(remainingIncome, bracket.max - bracket.min);
            tax += taxableAtThisBracket * bracket.rate;
            remainingIncome -= taxableAtThisBracket;
        }

        tax = Math.round(tax * 100) / 100;
        const effectiveRate = taxableIncome > 0 ? (tax / taxableIncome) * 100 : 0;

        return {
            taxableIncome,
            tax,
            effectiveRate: Math.round(effectiveRate * 100) / 100,
            formula: `Thuế TNCN được tính theo bậc thang lũy tiến`
        };
    },

    /**
     * Calculate cash flow summary
     * @param {Array} transactions - Array of transactions
     * @param {string} period - Period filter ('month', 'quarter', 'year')
     * @param {Date} startDate - Start date for calculation
     * @param {Date} endDate - End date for calculation
     * @returns {object} Cash flow summary
     */
    calculateCashFlow(transactions, period = 'month', startDate = null, endDate = null) {
        if (!Array.isArray(transactions)) {
            return { totalIncome: 0, totalExpenses: 0, netCashFlow: 0, transactionCount: 0 };
        }

        // Filter transactions by date range if provided
        let filteredTransactions = transactions;
        if (startDate && endDate) {
            filteredTransactions = transactions.filter(t => {
                const transactionDate = new Date(t.date);
                return transactionDate >= startDate && transactionDate <= endDate;
            });
        }

        const summary = filteredTransactions.reduce((acc, transaction) => {
            const amount = transaction.totalAmount || transaction.amount || 0;
            
            if (transaction.type === 'income') {
                acc.totalIncome += amount;
                acc.incomeTransactions++;
            } else if (transaction.type === 'expense') {
                acc.totalExpenses += amount;
                acc.expenseTransactions++;
            }
            
            acc.transactionCount++;
            return acc;
        }, {
            totalIncome: 0,
            totalExpenses: 0,
            incomeTransactions: 0,
            expenseTransactions: 0,
            transactionCount: 0
        });

        // Round amounts
        summary.totalIncome = Math.round(summary.totalIncome * 100) / 100;
        summary.totalExpenses = Math.round(summary.totalExpenses * 100) / 100;
        summary.netCashFlow = Math.round((summary.totalIncome - summary.totalExpenses) * 100) / 100;

        return summary;
    },

    /**
     * Calculate running balance
     * @param {Array} transactions - Sorted array of transactions
     * @param {number} initialBalance - Initial balance
     * @returns {Array} Transactions with running balance
     */
    calculateRunningBalance(transactions, initialBalance = 0) {
        if (!Array.isArray(transactions)) return [];

        let runningBalance = initialBalance;
        
        return transactions.map(transaction => {
            const amount = transaction.totalAmount || transaction.amount || 0;
            
            if (transaction.type === 'income') {
                runningBalance += amount;
            } else if (transaction.type === 'expense') {
                runningBalance -= amount;
            }
            
            return {
                ...transaction,
                runningBalance: Math.round(runningBalance * 100) / 100
            };
        });
    },

    /**
     * Calculate financial ratios and metrics
     * @param {object} financialData - Financial data object
     * @returns {object} Financial ratios
     */
    calculateFinancialMetrics(financialData) {
        const {
            totalRevenue = 0,
            totalExpenses = 0,
            totalAssets = 0,
            totalLiabilities = 0,
            equity = 0
        } = financialData;

        const netIncome = totalRevenue - totalExpenses;
        const profitMargin = totalRevenue > 0 ? (netIncome / totalRevenue) * 100 : 0;
        const debtToEquity = equity > 0 ? totalLiabilities / equity : 0;
        const returnOnAssets = totalAssets > 0 ? (netIncome / totalAssets) * 100 : 0;

        return {
            netIncome: Math.round(netIncome * 100) / 100,
            profitMargin: Math.round(profitMargin * 100) / 100,
            debtToEquity: Math.round(debtToEquity * 100) / 100,
            returnOnAssets: Math.round(returnOnAssets * 100) / 100,
            formulas: {
                netIncome: 'Lợi nhuận ròng = Tổng doanh thu - Tổng chi phí',
                profitMargin: 'Tỷ suất lợi nhuận = (Lợi nhuận ròng / Doanh thu) × 100%',
                debtToEquity: 'Tỷ số nợ/vốn = Tổng nợ / Vốn chủ sở hữu',
                returnOnAssets: 'ROA = (Lợi nhuận ròng / Tổng tài sản) × 100%'
            }
        };
    },

    /**
     * Calculate depreciation (Vietnamese straight-line method)
     * @param {number} cost - Original cost of asset
     * @param {number} salvageValue - Salvage value
     * @param {number} usefulLife - Useful life in years
     * @returns {object} Depreciation calculation
     */
    calculateDepreciation(cost, salvageValue = 0, usefulLife) {
        if (typeof cost !== 'number' || typeof usefulLife !== 'number' || usefulLife <= 0) {
            return { annualDepreciation: 0, monthlyDepreciation: 0, depreciationRate: 0 };
        }

        const depreciableAmount = cost - salvageValue;
        const annualDepreciation = Math.round((depreciableAmount / usefulLife) * 100) / 100;
        const monthlyDepreciation = Math.round((annualDepreciation / 12) * 100) / 100;
        const depreciationRate = cost > 0 ? (annualDepreciation / cost) * 100 : 0;

        return {
            cost,
            salvageValue,
            usefulLife,
            depreciableAmount,
            annualDepreciation,
            monthlyDepreciation,
            depreciationRate: Math.round(depreciationRate * 100) / 100,
            formula: `Khấu hao hàng năm = (${AppFormatters.formatCurrency(cost)} - ${AppFormatters.formatCurrency(salvageValue)}) ÷ ${usefulLife} năm = ${AppFormatters.formatCurrency(annualDepreciation)}`
        };
    },

    /**
     * Calculate compound interest
     * @param {number} principal - Principal amount
     * @param {number} rate - Annual interest rate (as percentage)
     * @param {number} time - Time in years
     * @param {number} compoundFrequency - Compounding frequency per year
     * @returns {object} Interest calculation
     */
    calculateCompoundInterest(principal, rate, time, compoundFrequency = 12) {
        if (typeof principal !== 'number' || typeof rate !== 'number' || typeof time !== 'number') {
            return { principal: 0, interest: 0, total: 0 };
        }

        const rateDecimal = rate / 100;
        const amount = principal * Math.pow((1 + rateDecimal / compoundFrequency), compoundFrequency * time);
        const interest = amount - principal;

        return {
            principal: Math.round(principal * 100) / 100,
            rate,
            time,
            compoundFrequency,
            interest: Math.round(interest * 100) / 100,
            total: Math.round(amount * 100) / 100,
            formula: `A = P(1 + r/n)^(nt) = ${AppFormatters.formatCurrency(principal)}(1 + ${rate}%/${compoundFrequency})^(${compoundFrequency}×${time})`
        };
    },

    /**
     * Validate calculation inputs
     * @param {object} inputs - Input values to validate
     * @param {Array} requiredFields - Required field names
     * @returns {object} Validation result
     */
    validateInputs(inputs, requiredFields = []) {
        const errors = {};

        requiredFields.forEach(field => {
            if (inputs[field] === null || inputs[field] === undefined || inputs[field] === '') {
                errors[field] = `${field} is required`;
            } else if (typeof inputs[field] === 'string') {
                const numericValue = parseFloat(inputs[field]);
                if (isNaN(numericValue)) {
                    errors[field] = `${field} must be a valid number`;
                }
            }
        });

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AppCalculations;
} else if (typeof window !== 'undefined') {
    window.AppCalculations = AppCalculations;
}
